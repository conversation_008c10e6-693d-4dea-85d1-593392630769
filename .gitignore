# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Node modules
/node_modules/

# Next.js build output
/.next/
/out/
/build/

# Vercel
/.vercel/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

# Logs
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
*.log

# OS/System files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# TypeScript
*.tsbuildinfo
.next-env.d.ts

# Coverage directory
coverage/
.nyc_output/
lcov-report/

# Test snapshots
__snapshots__/
*.snap

# Lock files (alternate)
package-lock.json
yarn.lock

# pnpm store
.pnpm/

# Miscellaneous build output
dist/
out-tsc/