# 数据库设计文档

## 一、设计思路

1. **分类表（categories）**：存储首页左侧的工具分类，用于导航和筛选。
2. **AI 工具表（tools）**：存储审核通过并正式展示在前端的 AI 工具信息。
3. **工具提交记录表（tool_submissions）**：存储用户提交的工具，审核完成后再将其数据插入到 `tools` 表中。

---

## 二、表结构及字段说明

### 1. categories（分类表）

用途：存储首页左侧分类数据，支持多级分类（可选）、排序、图标等。

| 字段名      | 类型                     | 约束                                   | 描述                                      |
| ----------- | ------------------------ | -------------------------------------- | ----------------------------------------- |
| id          | UUID                     | PRIMARY KEY, DEFAULT gen_random_uuid() | 分类唯一标识                              |
| name        | VARCHAR(100)             | NOT NULL                               | 分类名称                                  |
| slug        | VARCHAR(100)             | NOT NULL, UNIQUE                       | URL 友好标识                              |
| icon_name   | VARCHAR(100)             |                                        | 图标组件名（可选），例如 "ChatBubbleIcon" |
| icon_url    | TEXT                     |                                        | 图标地址（备用可选）                      |
| description | TEXT                     |                                        | 分类描述（可选）                          |
| order_index | INT                      | NOT NULL, DEFAULT 0                    | 排序值，越小排越前                        |
| created_at  | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                | 创建时间                                  |
| updated_at  | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                | 最后更新时间                              |

### 2. tools（AI 工具表）

用途：存储所有已审核通过的 AI 工具，并供前端展示。

| 字段名       | 类型                     | 约束                                                   | 描述                                      |
| ------------ | ------------------------ | ------------------------------------------------------ | ----------------------------------------- |
| id           | UUID                     | PRIMARY KEY, DEFAULT gen_random_uuid()                 | 工具唯一标识                              |
| name         | VARCHAR(255)             | NOT NULL                                               | 工具名称                                  |
| slug         | VARCHAR(255)             | NOT NULL, UNIQUE                                       | URL 友好标识                              |
| url          | TEXT                     | NOT NULL                                               | 工具网址                                  |
| logo_url     | TEXT                     |                                                        | Logo 地址                                 |
| cover_url    | TEXT                     |                                                        | 封面图地址                                |
| category_id  | UUID                     | NOT NULL, REFERENCES categories(id) ON DELETE RESTRICT | 所属分类                                  |
| summary      | TEXT                     |                                                        | 简要描述（用于列表页卡片展示）            |
| description  | TEXT                     |                                                        | 详细描述（支持 Markdown，用于详情页简介） |
| faqs         | TEXT                     |                                                        | 常见问题（支持 Markdown，用于详情页 FAQ） |
| status       | VARCHAR(20)              | NOT NULL, DEFAULT 'published'                          | 状态：`published`/`archived` 等           |
| is_popular   | BOOLEAN                  | NOT NULL, DEFAULT FALSE                                | 是否热门                                  |
| is_new       | BOOLEAN                  | NOT NULL, DEFAULT TRUE                                 | 是否新工具                                |
| submitted_by | UUID                     | REFERENCES auth.users(id) ON DELETE SET NULL           | 提交者（审核前为提交者，审核后保留）      |
| created_at   | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                                | 创建时间                                  |
| updated_at   | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                                | 最后更新时间                              |

### 3. tool_submissions（工具提交记录表）

用途：记录用户提交的工具，供管理员审核；审核通过后再将记录插入 `tools` 表中。

| 字段名          | 类型                     | 约束                                                   | 描述                                      |
| --------------- | ------------------------ | ------------------------------------------------------ | ----------------------------------------- |
| id              | UUID                     | PRIMARY KEY, DEFAULT gen_random_uuid()                 | 提交记录唯一标识                          |
| user_id         | UUID                     | NOT NULL, REFERENCES auth.users(id) ON DELETE CASCADE  | 提交者                                    |
| name            | VARCHAR(255)             | NOT NULL                                               | 提交的工具名称                            |
| slug            | VARCHAR(255)             | NOT NULL                                               | URL 友好标识，审核通过后要保证唯一        |
| url             | TEXT                     | NOT NULL                                               | 提交的工具网址                            |
| logo_url        | TEXT                     |                                                        | Logo 地址                                 |
| cover_url       | TEXT                     |                                                        | 封面图地址                                |
| category_id     | UUID                     | NOT NULL, REFERENCES categories(id) ON DELETE RESTRICT | 选择的分类                                |
| summary         | TEXT                     |                                                        | 简要描述                                  |
| description     | TEXT                     |                                                        | 详细描述                                  |
| faqs            | TEXT                     |                                                        | 常见问题（Markdown 格式）                 |
| status          | VARCHAR(20)              | NOT NULL, DEFAULT 'pending'                            | 审核状态：`pending`/`approved`/`rejected` |
| review_comments | TEXT                     |                                                        | 审核意见                                  |
| reviewed_by     | UUID                     | REFERENCES auth.users(id) ON DELETE SET NULL           | 审核人                                    |
| submitted_at    | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                                | 提交时间                                  |
| reviewed_at     | TIMESTAMP WITH TIME ZONE |                                                        | 审核时间                                  |

---

## 三、ER 图（示意）

```plaintext
auth.users (supabase 内置)
      │
      ├─<tool_submissions>.user_id
      │
      ├─<tool_submissions>.reviewed_by
      │
      └─<tools>.submitted_by

categories.id
   │
   ├─<tools>.category_id
   │
   └─<tool_submissions>.category_id
```

## 四、建表 SQL

```sql
-- 创建分类表 categories
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  icon_name VARCHAR(100),
  icon_url TEXT,
  description TEXT,
  order_index INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 启用行级安全策略（RLS）
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 查看策略：允许所有用户读取 categories 表数据
CREATE POLICY "public_read_categories"
  ON categories
  FOR SELECT
  USING (true);


-- 创建 AI 工具表 tools
CREATE TABLE IF NOT EXISTS tools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  url TEXT NOT NULL,
  logo_url TEXT,
  cover_url TEXT,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE RESTRICT,
  summary TEXT,
  description TEXT,
  faqs TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'published',
  is_popular BOOLEAN NOT NULL DEFAULT FALSE,
  is_new BOOLEAN NOT NULL DEFAULT TRUE,
  submitted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 启用行级安全策略（RLS）
ALTER TABLE tools ENABLE ROW LEVEL SECURITY;

-- 查看策略：允许所有用户读取 tools 表数据
CREATE POLICY "public_read_tools"
  ON tools
  FOR SELECT
  USING (true);

-- 创建索引以优化查询性能
CREATE INDEX idx_tools_category_id ON tools(category_id);
CREATE INDEX idx_tools_status ON tools(status);


-- 创建工具提交记录表 tool_submissions
CREATE TABLE IF NOT EXISTS tool_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  url TEXT NOT NULL,
  logo_url TEXT,
  cover_url TEXT,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE RESTRICT,
  summary TEXT,
  description TEXT,
  faqs TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  review_comments TEXT,
  reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  submitted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  reviewed_at TIMESTAMP WITH TIME ZONE
);

-- 启用行级安全策略（RLS）
ALTER TABLE tool_submissions ENABLE ROW LEVEL SECURITY;

-- 查看策略：允许用户读取自己的提交记录
CREATE POLICY "user_read_tool_submissions"
  ON tool_submissions
  FOR SELECT
  USING (auth.uid() = user_id);

-- 插入策略：允许用户插入自己的提交记录
CREATE POLICY "user_insert_tool_submissions"
  ON tool_submissions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- 创建索引以优化查询性能
CREATE INDEX idx_tool_submissions_user_id ON tool_submissions(user_id);
CREATE INDEX idx_tool_submissions_category_id ON tool_submissions(category_id);
CREATE INDEX idx_tool_submissions_status ON tool_submissions(status);
```

## 五、字段更新说明

### 新增字段

- **tools.summary**: 用于列表页卡片的简要描述
- **tools.faqs**: 存储常见问题的 Markdown 内容
- **tool_submissions.summary**: 提交时的简要描述
- **tool_submissions.faqs**: 提交时的常见问题

### 字段用途

- **summary**: 在首页工具卡片中显示，建议控制在 50-100 字符内
- **description**: 在详情页"工具简介"部分显示，支持 Markdown 格式
- **faqs**: 在详情页"常见问题"部分显示，支持 Markdown 格式

## 六、小结

- 三张表分别负责分类、已审核工具和提交记录，使用外键关联保证数据一致性。
- 前端可根据 `categories` 渲染侧栏，用户提交写入 `tool_submissions`，审核后插入 `tools`。
- 新增的 `summary` 和 `faqs` 字段分别用于列表展示和详情页 FAQ，支持更好的内容组织。
- 熟悉 SQL 外键、状态字段设计和事务操作，有助于后续功能扩展。
