-- 创建存储桶的 SQL 脚本
-- 需要在 Supabase SQL Editor 中执行

-- 创建 tool-logos 存储桶（公开读取）
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tool-logos',
  'tool-logos',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- 创建 tool-covers 存储桶（公开读取）
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tool-covers',
  'tool-covers',
  true,
  10485760, -- 10MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- 设置 tool-logos 存储桶的访问策略
-- 允许所有用户读取
CREATE POLICY "public_read_tool_logos"
  ON storage.objects
  FOR SELECT
  USING ( bucket_id = 'tool-logos' );

-- 允许认证用户上传和删除
CREATE POLICY "auth_write_tool_logos"
  ON storage.objects
  FOR INSERT
  WITH CHECK ( 
    bucket_id = 'tool-logos' 
    AND auth.role() = 'authenticated' 
  );

CREATE POLICY "auth_update_tool_logos"
  ON storage.objects
  FOR UPDATE
  USING ( 
    bucket_id = 'tool-logos' 
    AND auth.role() = 'authenticated' 
  );

CREATE POLICY "auth_delete_tool_logos"
  ON storage.objects
  FOR DELETE
  USING ( 
    bucket_id = 'tool-logos' 
    AND auth.role() = 'authenticated' 
  );

-- 设置 tool-covers 存储桶的访问策略
-- 允许所有用户读取
CREATE POLICY "public_read_tool_covers"
  ON storage.objects
  FOR SELECT
  USING ( bucket_id = 'tool-covers' );

-- 允许认证用户上传和删除
CREATE POLICY "auth_write_tool_covers"
  ON storage.objects
  FOR INSERT
  WITH CHECK ( 
    bucket_id = 'tool-covers' 
    AND auth.role() = 'authenticated' 
  );

CREATE POLICY "auth_update_tool_covers"
  ON storage.objects
  FOR UPDATE
  USING ( 
    bucket_id = 'tool-covers' 
    AND auth.role() = 'authenticated' 
  );

CREATE POLICY "auth_delete_tool_covers"
  ON storage.objects
  FOR DELETE
  USING ( 
    bucket_id = 'tool-covers' 
    AND auth.role() = 'authenticated' 
  ); 