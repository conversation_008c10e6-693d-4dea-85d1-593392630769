# 提交功能故障排除指南

## 问题：提交表单后没有任何提示

### 已修复的问题

1. **Toast 组件未引入** ✅ 已修复
   - 问题：根布局 `app/layout.tsx` 中缺少 `<Toaster />` 组件
   - 解决方案：已在根布局中添加 `<Toaster />` 组件

### 需要检查的问题

#### 1. Supabase Storage 存储桶未创建

**症状：** 文件上传失败，控制台显示存储桶不存在的错误

**解决方案：**

1. 登录 Supabase Dashboard
2. 进入 Storage 页面
3. 在 SQL Editor 中执行以下脚本：

```sql
-- 参考 docs/dev/create-storage-buckets.sql 文件
```

或者手动创建存储桶：

- 创建 `tool-logos` 存储桶（公开，5MB 限制）
- 创建 `tool-covers` 存储桶（公开，10MB 限制）

#### 2. 用户未登录

**症状：** API 返回 401 错误

**解决方案：**

1. 确保用户已登录
2. 检查 Supabase 认证状态
3. 访问 `/test-submission` 页面测试认证状态

#### 3. 数据库表不存在或字段不匹配

**症状：** API 返回 500 错误，控制台显示数据库错误

**解决方案：**

1. 检查 `tool_submissions` 表是否存在
2. 确认表字段与代码中的字段匹配
3. 参考 `docs/dev/database-design.md` 创建表

### 调试步骤

#### 1. 测试 Toast 功能

访问 `/test-submission` 页面，点击测试按钮，检查：

- Toast 通知是否正常显示
- 控制台是否有错误信息

#### 2. 检查浏览器控制台

打开浏览器开发者工具，查看：

- Console 标签页的错误信息
- Network 标签页的 API 请求状态
- 文件上传的响应状态

#### 3. 检查 API 响应

使用 curl 或 Postman 测试 API：

```bash
# 测试 GET 请求（需要登录）
curl -X GET http://localhost:3000/api/tool_submissions

# 测试 POST 请求（需要登录）
curl -X POST http://localhost:3000/api/tool_submissions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试工具",
    "slug": "test-tool",
    "url": "https://example.com",
    "category_id": "有效的分类ID",
    "summary": "测试概要",
    "description": "测试描述"
  }'
```

### 常见错误及解决方案

#### 错误 1: "Unauthorized"

- **原因：** 用户未登录
- **解决：** 确保用户已通过 Supabase Auth 登录

#### 错误 2: "Missing required fields"

- **原因：** 必填字段为空
- **解决：** 检查表单验证逻辑，确保所有必填字段都有值

#### 错误 3: "Failed to create submission"

- **原因：** 数据库插入失败
- **解决：** 检查数据库表结构和字段类型

#### 错误 4: Storage 相关错误

- **原因：** 存储桶不存在或权限不足
- **解决：** 创建存储桶并设置正确的访问策略

### 验证清单

- [ ] Toaster 组件已添加到根布局
- [ ] Supabase Storage 存储桶已创建
- [ ] 用户已登录
- [ ] 数据库表结构正确
- [ ] API 路由正常工作
- [ ] 前端表单验证正常
- [ ] 文件上传功能正常

### 联系支持

如果问题仍然存在，请提供：

1. 浏览器控制台的完整错误信息
2. Network 标签页中的 API 请求详情
3. 当前的用户登录状态
4. 使用的浏览器和版本信息
