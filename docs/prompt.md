我正在创建一个 AI 工具导航网站，页面布局与功能如下：

### 一、导航页面结构

- 左侧为固定的分类侧边栏，点击某个分类后，会自动滚动定位到当前页面中该分类所在的位置。

- 右侧区域包含以下模块：

  1. 页面顶部展示：

    - 热门工具

    - 最新收录

  2. 分类展示区：展示当前页面各分类下的工具列表（与左侧分类联动）

  3. 页面右上角包含：

    - 站内搜索功能，可按关键词或分类筛选工具

    - 登录按钮（未登录状态）或个人中心按钮（登录后状态）

  4. 左侧分类栏底部添加“提交站点”按钮：

    - 若用户已登录，点击跳转到个人中心中的“提交表单”Tab

    - 若用户未登录，点击自动跳转登录页面，登录成功后进入表单页面

- 用户点击某个 AI 工具卡片后，新标签页打开工具详情页，包含：

  - 顶部封面图（参考图2）

  - 工具标题、所属分类

  - 打开网站按钮（跳转至官网）

  - 下方内容区包括工具简要介绍（content）和常见问题（QA）

---

### 二、用户系统与个人中心功能

- 登录后，用户可进入“个人中心”，包含三个 Tab：

  1. **提交历史**

    - 展示用户提交的工具列表

    - 包括工具名、提交时间、审核状态（审核中 / 已发布）

  2. **提交表单**

    - 用于添加新的 AI 工具，字段包括：

      - 网站名称、网站的slug、网址

      - 网站 Logo（上传组件）

      - 网站预览图（上传组件）

      - 工具概要（简短描述）

      - 工具详细介绍文档（Markdown/富文本）

    - 用户点击“提交”后，该工具状态将为“审核中”，等待管理员审核

  3. **个人信息**

    - 展示用户基本资料

    - 提供“退出登录”按钮