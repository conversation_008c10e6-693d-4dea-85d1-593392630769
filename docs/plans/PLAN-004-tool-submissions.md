# 开发计划: Tool Submissions 功能

## 一、需求理解

- 需求复述：
  1. 提交历史页面不再使用模拟数据，而是通过 Supabase 从 `tool_submissions` 表中按当前用户拉取真实提交记录；
  2. 提交表单功能已完成前端部分，现需实现后端写入：将用户填写的表单字段（包括 Logo/封面在前端已上传至 Supabase Storage 并获得对应 URL）插入到 `tool_submissions` 表中。

> 如有不明确可继续沟通。

## 二、现有代码梳理

1. Supabase 服务端客户端创建：
   ```typescript
   // lib/supabase/server.ts
   export const createClient = async () => {
     /* createServerClient(...) */
   };
   ```
2. 工具列表与详情 API：
   ```typescript
   // app/api/tools/route.ts
   export async function GET(request: NextRequest) {
     /* supabase.from('tools').select(...) */
   }
   ```
3. 数据库表结构（参考 docs/dev/database-design.md）
   - `tool_submissions` 表字段：
     ```sql
     id, user_id, name, slug, url, logo_url, cover_url,
     category_id, summary, description, faqs,
     status (默认 'pending'), review_comments, reviewed_by,
     submitted_at, reviewed_at
     ```
4. 用户认证已在 middleware 中完成，API 可通过 `supabase.auth.getUser()` 获取当前用户。

## 三、开发方案

1. 新增 API 路由：`app/api/tool_submissions/route.ts`，支持：
   - `GET`：获取当前用户提交历史；
   - `POST`：插入表单数据到 `tool_submissions`。
2. `GET` 实现：
   - 从 Supabase 拉取用户的 `tool_submissions`，按 `submitted_at` 倒序排序，连带分类信息（join categories）。
3. `POST` 实现：
   - 从请求体中读取字段：`name, slug, url, logo_url, cover_url, category_id, summary, description, faqs`；
   - 调用 `supabase.from('tool_submissions').insert({... , user_id: 当前用户.id})`；
   - 返回 `201 Created` 与插入数据。
4. 错误处理：鉴权失败返回 `401`；插入错误返回 `500`；字段校验（可选，前端已校验）。

```mermaid
sequenceDiagram
    participant F as 前端 (Form)
    participant A as API: /api/tool_submissions
    participant S as Supabase

    F->>A: POST 提交表单数据（含已上传文件 URL）
    A->>S: INSERT INTO tool_submissions
    S-->>A: 返回插入结果
    A-->>F: 201 + 新记录

    F->>A: GET 获取提交历史
    A->>S: SELECT * FROM tool_submissions WHERE user_id
    S-->>A: 返回记录列表
    A-->>F: 200 + 列表
```

## 四、开发计划

- [x] 1. 新建 API 路由文件并搭建基础框架 (1h) ✅ 已完成
- [x] 2. 实现 GET 接口并本地联调 (1h) ✅ 已完成
- [x] 3. 实现 POST 接口并联调，包括 Supabase 写入 (1.5h) ✅ 已完成
- [x] 4. 修改前端提交表单，集成真实 API 调用和文件上传 (1h) ✅ 已完成
- [x] 5. 修改前端提交历史，使用真实数据而非模拟数据 (1h) ✅ 已完成
- [ ] 6. 端到端测试：用户登录 → 提交表单 → 查看历史 (0.5h)
- [ ] 7. 代码 Review 与文档更新 (0.5h)

## 五、测试用例

1. GET: 未登录时返回 401 ✅；登录后正确拉取 own submissions 数量与字段。
2. POST:
   - 必填字段缺失返回错误；
   - 登录状态下提交正确数据后能在 GET 中获取到新记录；
   - 插入失败时返回 500。
3. 边界场景：无记录时返回空数组。

## 六、实现总结

### 已完成功能

1. **API 路由实现** (`app/api/tool_submissions/route.ts`)：

   - ✅ GET 方法：获取当前用户的提交历史，包含分类信息
   - ✅ POST 方法：创建新的工具提交记录
   - ✅ 用户认证：通过 `supabase.auth.getUser()` 验证用户身份
   - ✅ 错误处理：401 未授权、400 参数错误、500 服务器错误

2. **前端提交表单更新** (`components/submission-form.tsx`)：

   - ✅ 集成真实分类数据（从 `/api/categories` 获取）
   - ✅ 文件上传到 Supabase Storage（tool-logos 和 tool-covers 桶）
   - ✅ 表单数据提交到 `/api/tool_submissions`
   - ✅ 错误处理和用户反馈（Toast 通知）
   - ✅ 表单重置功能

3. **前端提交历史更新** (`components/submission-history.tsx`)：
   - ✅ 从 `/api/tool_submissions` 获取真实数据
   - ✅ 加载状态和错误处理
   - ✅ 支持多种审核状态（pending、approved、rejected）
   - ✅ 详情弹窗显示完整提交信息

### 技术实现要点

1. **文件上传策略**：

   - 使用临时 UUID 作为文件夹名称
   - 时间戳命名避免缓存问题
   - 分离 Logo 和封面图到不同存储桶

2. **数据库设计**：

   - 遵循现有表结构设计
   - 支持关联查询（JOIN categories）
   - 行级安全策略（RLS）保护用户数据

3. **前端状态管理**：
   - 使用 React Hooks 管理组件状态
   - 错误边界和加载状态处理
   - 表单验证和用户体验优化

## 七、反思

- ✅ 本计划依托项目既有 Supabase 服务端客户端与 App Router API 规范；
- ✅ 使用 `supabase.auth.getUser()` 做简洁鉴权；
- ✅ SQL 注入风险低，采用 Supabase 参数化查询；
- ✅ 符合 Next.js 15 App Router 与 TypeScript 最佳实践；
- ✅ 文件上传集成 Supabase Storage，遵循存储设计规范；
- ✅ 前端组件保持了原有的 UI/UX 设计，仅替换数据源；
- ✅ 错误处理完善，用户体验友好。

### 下一步建议

1. **端到端测试**：建议在真实环境中测试完整流程
2. **性能优化**：考虑添加分页功能（当提交记录较多时）
3. **功能扩展**：可考虑添加提交记录的编辑/删除功能
4. **监控告警**：生产环境建议添加 API 监控和错误告警
