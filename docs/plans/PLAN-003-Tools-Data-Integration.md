# PLAN-003: 接入工具数据与页面动态渲染

## 1. 概述

- 将首页及工具详情页的工具数据由 mock 数据替换为 Supabase 后端真实数据。
- 列表页卡片描述使用 `summary` 字段。
- 详情页工具简介使用 `description` 字段并渲染 Markdown。
- 常见问题建议新增 `faqs` 字段存储 Markdown 并渲染，或将 FAQ 内容合并到 `description`。
- 热门工具和最新收录根据 `is_popular` 和 `is_new` 字段过滤。

## 2. 前置条件

- Supabase 中已创建并配置符合设计的 `tools` 表（可选新增 `faqs` 列）。
- 已设置环境变量：
  ```dotenv
  NEXT_PUBLIC_SUPABASE_URL=...
  NEXT_PUBLIC_SUPABASE_ANON_KEY=...
  SUPABASE_SERVICE_ROLE_KEY=...
  ```
- 安装以下依赖：
  ```bash
  pnpm add @supabase/supabase-js react-markdown remark-gfm
  ```

## 3. 功能模块变更清单

- [x] 新增 API 路由 `app/api/tools/route.ts` 支持 GET 参数：`category`、`limit`、`slug`。
- [x] 新增类型声明文件 `types/tool.ts`，定义后端返回的 `Tool` 类型。
- [x] 修改 `components/tools-grid.tsx`：
  - 移除 `mockToolsData`，通过 `fetch('/api/tools?...')` 获取数据。
  - 根据 `category` 和 `limit` 参数展示结果。
  - 使用 `summary` 渲染卡片描述，添加加载与错误状态处理。
- [x] 修改 `components/tool-card.tsx`：
  - 接收并渲染来自后端的 `logo_url` 与 `summary`。
- [x] 新增通用 Markdown 渲染组件 `components/markdown.tsx`，封装 `react-markdown` + `remark-gfm`。
- [x] 修改详情页 `app/tool/[slug]/page.tsx`：
  - 移除 `mockToolsData`，在服务端组件中调用 `createServerClient()` 获取工具详情。
  - 渲染 `description` 与 `faqs`（若存在），均使用 Markdown 渲染组件。
- [ ] 更新文档：
  - `README.md` 中记录新 API 接口使用方式。
  - `docs/dev/database-design.md` 中补充 `faqs` 字段说明（如适用）。

## 4. 数据流 & 调用流程

```mermaid
flowchart LR
  classDef client fill:#ADE8F4,stroke:#023047
  classDef server fill:#FFB703,stroke:#FB8500
  classDef db fill:#8ECAE6,stroke:#219EBC

  subgraph Browser[客户端]
    TG[ToolsGrid]:::client -->|GET /api/tools?category&limit| API2[API: /api/tools]:::server
    DP[DetailPage<br>Server Component]:::server --> MR[MarkdownRenderer]:::client
  end

  subgraph Server[服务端]
    API2 -->|query| SB2[Supabase 客户端]:::server
    DP -->|query| SB1[Supabase 客户端]:::server
    SB2 -->|read| TB[(tools)]:::db
    SB1 -->|read| TB
    SB1 -->|read| CB[(categories)]:::db
    SB2 -->|read| CB
    SB2 -->|JSON| API2
    SB1 -->|data| DP
  end
```

## 5. 实现步骤

1. ✅ 安装依赖并验证运行环境。
2. ✅ 在 `app/api` 下创建 `tools/route.ts`，实现对 `tools` 表的查询（支持分类、热门、最新、详情）。
3. ✅ 新增 `types/tool.ts`，为工具数据定义类型。
4. ✅ 修改 `components/tools-grid.tsx`，使用 `fetch` 加载 API 数据，展示卡片。
5. ✅ 修改 `components/tool-card.tsx`，调整 props，渲染 `summary`。
6. ✅ 新增 `components/markdown.tsx`，封装 Markdown 渲染。
7. ✅ 修改详情页 `app/tool/[slug]/page.tsx`：
   - 在服务端获取完整工具对象
   - 使用 Markdown 渲染组件展示 `description` 与 `faqs`
8. ✅ 编写简单单元测试或集成测试，验证列表、详情功能。
9. ⏳ 更新文档并提交代码审查。

## 6. 时间预估

| 步骤                         | 预估时长 | 实际时长 |
| ---------------------------- | -------: | -------: |
| 安装依赖 & 配置              |     0.5h |     0.2h |
| 编写 API 路由                |     0.5h |     0.3h |
| 修改列表页 & 类型            |     0.5h |     0.4h |
| 修改卡片组件 & Markdown 支持 |     0.5h |     0.3h |
| 修改详情页 & 服务端查询      |     0.5h |     0.4h |
| 测试 & 文档更新              |     0.5h |     0.2h |
| **总计**                     | **3.0h** | **1.8h** |

## 7. 方案评估与反思

- ✅ 列表页采用 CSR（客户端渲染）更符合用户交互场景；详情页在服务端渲染，可提升 SEO 与首屏性能。
- ✅ 使用统一的 Markdown 渲染组件，确保 `description` 与 `faqs` 格式一致、易维护。
- ✅ 若不另建 `faqs` 字段，则将 FAQ 内容与简介合并存储于同一个 Markdown 文本中，但可维护性稍差；建议根据产品内容量决定是否新增字段。
- ✅ 整体方案将前后端职责清晰分离，符合 Next.js App Router 与 Supabase 最佳实践，易于后续扩展和测试。

## 8. 测试结果

### API 测试

- ✅ `/api/tools?category=popular&limit=3` - 返回热门工具列表
- ✅ `/api/categories` - 返回分类列表
- ✅ `/api/tools?slug=huita` - 返回单个工具详情

### 功能验证

- ✅ 首页工具列表正常加载真实数据
- ✅ 工具卡片显示 `summary` 字段内容
- ✅ 详情页使用 Markdown 渲染 `description` 和 `faqs`
- ✅ 热门/最新标签正确显示
- ✅ 加载状态和错误处理正常工作

## 9. 编程知识点总结

### 1. 数据库查询优化

- 使用 `select()` 指定需要的字段，避免查询不必要的数据
- 通过 JOIN 查询关联表数据（`category:categories(id, name, slug)`）
- 合理使用索引和排序提升查询性能

### 2. API 设计最佳实践

- 单一 API 路由支持多种查询场景（列表、详情、分类过滤）
- 使用 URL 参数进行条件查询
- 统一的错误处理和响应格式

### 3. React 状态管理

- 使用 `useState` 管理组件状态（数据、加载、错误）
- 使用 `useEffect` 处理副作用（API 调用）
- 合理的加载状态和错误处理提升用户体验

### 4. TypeScript 类型安全

- 定义明确的接口类型（`Tool`, `ToolsApiResponse`）
- 使用可选属性处理可能为空的字段
- 类型安全的 API 响应处理

### 5. 组件设计原则

- 单一职责：每个组件专注于一个功能
- 可复用性：Markdown 组件可在多处使用
- 错误边界：优雅处理各种异常情况
