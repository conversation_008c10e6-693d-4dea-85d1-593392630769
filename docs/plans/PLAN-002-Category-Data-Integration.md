# PLAN-002: 接入后台分类数据开发方案

## 1. 概述

- 目前前端使用静态 Mock 数据（`mock-data.ts` 和 `category-sidebar.tsx` 中的数组），需改为 Supabase 后端获取分类与工具数据，实现动态、真实数据接入。

## 2. 前置条件

- 确保已在 Supabase 控制台创建 `categories` 和 `tools` 表，符合 `docs/dev/database-design.md` 设计。
- 已配置环境变量：
  ```dotenv
  NEXT_PUBLIC_SUPABASE_URL=<YOUR_SUPABASE_URL>
  NEXT_PUBLIC_SUPABASE_ANON_KEY=<YOUR_SUPABASE_ANON_KEY>
  ```
- 安装依赖：
  ```bash
  pnpm add @supabase/supabase-js
  ```

## 3. 功能模块变更清单

- [x] 新增 `app/api/categories/route.ts` API 路由，提供分类数据。
- [x] 修改 `components/category-sidebar.tsx` ：调用 `/api/categories` ，替换静态数组。
- [x] 新增加载状态与错误提示 （在 Sidebar 中展示 Spinner 或提示文本）。
- [x] 修改 `app/page.tsx`：动态渲染分类区块，替换硬编码分类。
- [x] 新增类型定义 `types/category.ts` 和图标映射 `lib/icon-mapping.tsx`。
- [x] 本地测试：浏览器访问首页、分类切换与滚动功能验证。
- [ ] 代码审查与合并 （PR，含单元测试或端到端测试示例）。
<!-- 工具数据整合将在后续单独计划中完成 -->

## 4. 数据流 & 调用流程

```mermaid
flowchart LR
  classDef client fill:#ADE8F4,stroke:#023047
  classDef server fill:#FFB703,stroke:#FB8500
  classDef db fill:#8ECAE6,stroke:#219EBC

  subgraph Browser[客户端]
    CSR1[CategorySidebar]:::client -->|GET /api/categories| API1[api/categories]:::server
  end

  subgraph Server[服务端]
    API1 -->|query| SB[Supabase 客户端]:::server
    SB -->|read| DB[(categories)]:::db
    DB -->|response| SB
    SB -->|JSON| API1
  end
```

## 5. 实现步骤

1. **创建分类 API 路由**

   - `app/api/categories/route.ts`

2. **服务端查询实现**

   - 在 `route.ts` 中使用 `createServerClient()` 连接 Supabase。
   - `select(id, name, slug, order_index)` 并按 `order_index` 排序。
   - 错误处理：返回对应状态与错误信息。

3. **前端 Sidebar 数据调用**

   - 在 `components/category-sidebar.tsx` 中：
     - 移除静态 `categories` 数组，新增 `useState<Category[]>`。
     - 在 `useEffect` 调用 `fetch("/api/categories")`，维护 `categories`, `loading`, `error` 三个状态。
     - 渲染 `categories` 状态，加载时展示 Spinner，错误时展示提示。

4. **UI 优化**

   - 在 Sidebar 中添加加载指示 (Spinner)。
   - 错误时展示简易提示文案。

5. **测试与文档更新**

   - 本地运行 `pnpm dev`，验证 Sidebar 动态渲染。
   - 更新 `README.md` 或 `docs/dev/database-design.md` 中分类接口说明。

## 6. 时间预估

| 步骤                  | 预估时长 |
| --------------------- | -------- |
| API 分类路由开发      | 0.5h     |
| 服务端查询与错误处理  | 0.5h     |
| 前端 Sidebar 改造     | 0.5h     |
| UI Loading & 错误处理 | 0.5h     |
| 测试与文档更新        | 0.5h     |
| **总计**              | **2.5h** |

## 7. 方案评估与反思

- 该方案将前后端职责清晰分离，符合 Next.js App Router API 路由规范与单一职责原则。
- 使用服务端路由避免客户端暴露数据库逻辑，提升安全性与可维护性。
- 前端异步加载方案兼顾用户体验，加载状态和错误处理完善。
- 如果需要首屏 SEO，后续可考虑在 `page.tsx` 中使用服务端渲染提前注入首屏数据。

以上方案结构合理、易于扩展，符合最佳实践。
