# PLAN-001: Supabase 邮箱/密码登录注册集成方案

## 1. 概述

AI Tools Navigation 目前的登录/注册为模拟异步，需替换为 Supabase Auth 邮箱/密码认证，实现真实用户管理功能。

## 2. 前置条件

- 从 Supabase 控制台获取：

  - `SUPABASE_URL`（如 `https://xxxx.supabase.co`）
  - `SUPABASE_ANON_KEY`
  - （可选）`SUPABASE_SERVICE_ROLE_KEY` 用于服务端安全操作

- 在项目根目录创建并配置 `.env.local`：

  ```dotenv
  NEXT_PUBLIC_SUPABASE_URL=<YOUR_SUPABASE_URL>
  NEXT_PUBLIC_SUPABASE_ANON_KEY=<YOUR_SUPABASE_ANON_KEY>
  SUPABASE_SERVICE_ROLE_KEY=<YOUR_SERVICE_ROLE_KEY>
  ```

- 安装必要依赖：
  ```bash
  pnpm add @supabase/supabase-js @supabase/auth-helpers-nextjs
  ```

## 3. 文件变更与新增清单

### 3.1 新增 Supabase 工具包

1. **`lib/supabase/client.ts`**（浏览器端客户端）
2. **`lib/supabase/server.ts`**（服务端客户端，支持 SSR & Server Actions）
3. **`lib/supabase/middleware.ts`**（会话刷新与路由守卫可复用方法）

### 3.2 根中间件

- 根目录新增 **`middleware.ts`**，引用并调用 `lib/supabase/middleware.ts` 中的 `updateSession`。

### 3.3 Server Actions

- 新建 **`app/actions.ts`**，实现：

  - `signUpAction(formData)`
  - `signInAction(formData)`
  - `signOutAction()`
  - `forgotPasswordAction(formData)`
  - `resetPasswordAction(formData)`

  逻辑参考 with-supabase 示例，统一使用 `encodedRedirect(type, path, msg)` 或 `redirect()`。

### 3.4 OAuth 回调路由

- 新建 **`app/auth/callback/route.ts`**，处理参数 `?code=`：
  - 调用 `supabase.auth.exchangeCodeForSession(code)`
  - 设置 cookie
  - 重定向至原始页面或 `/account`

### 3.5 登录/注册页面改造

- 修改 **`app/login/page.tsx`**：

  - 去除模拟 `setTimeout` 逻辑
  - 使用 `<form action={signInAction}>` 和 `<form action={signUpAction}>`
  - 可在表单上直接使用 `name="email"`、`name="password"` 等字段
  - 去掉 `use client`，或保留用于 UI 交互并在客户端调用 Server Actions
  - 根据提交结果通过 URL 查询参数展示成功/错误信息

- 根据需要改造或保留 **`app/login/loading.tsx`**。

### 3.6 FormMessage 组件

- 新建 **`components/form-message.tsx`**：
  - 读取 `useSearchParams()` 中的 `?error` 和 `?success`
  - 使用 shadcn/ui 的 `Alert` 或 `Toast` 组件进行展示

### 3.7 个人中心/受保护页面

- 修改 **`app/account/page.tsx`** 或其他受保护页面：
  - 在页面 Server Component 中 `await createClient()`，再 `await supabase.auth.getUser()`
  - 根据 `user` 是否存在决定渲染或 `redirect('/login')`

## 4. 实现逻辑步骤

1. 安装依赖 & 配置 `.env.local`
2. 编写 `lib/supabase/*` 客户端与中间件
3. 在项目根添加 `middleware.ts`
4. 创建 `app/actions.ts`，实现所有 Auth 操作
5. 创建回调路由 `app/auth/callback/route.ts`
6. 重构 `app/login/page.tsx`，联动 Server Actions
7. 新增 `components/form-message.tsx`，处理全局提示
8. 重构 `app/account` 及其他需要登录保护的页面
9. 本地测试：注册、登录、忘记密码、重置密码、登出、路由守卫
10. 部署至 Vercel，验证环境变量及流程

## 5. 测试用例示例

- 注册：正确邮箱 → 邮件验证 → 回调 → 登录状态
- 登录：正确/错误凭证 → 成功跳转/错误提示
- 忘记密码：提交邮箱 → 邮件链接 → 重置页更新密码
- 登出：登录后访问 `/login` 或 `/account`
- 守卫：未登录访问 `/account` → 跳转 `/login`

## 6. 预估时间

| 步骤                       | 预估时长 |
| -------------------------- | -------- |
| 依赖安装 & 环境配置        | 0.5h     |
| Supabase 工具 & 中间件开发 | 1h       |
| Server Actions & 回调路由  | 1h       |
| 登录/注册页面重构          | 1.5h     |
| FormMessage & 样式完善     | 1h       |
| 受保护页面 & 测试          | 1h       |
| **总计**                   | **6h**   |
