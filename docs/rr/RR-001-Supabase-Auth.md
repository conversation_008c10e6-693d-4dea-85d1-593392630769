# RR-001: AI Tools Navigation 项目登录认证系统分析

## 一、AI Tools Navigation 项目中的登录/注册前端组件结构

### 路由与文件

- 登录/注册共用一个页面，位于 `app/login/page.tsx`，路由为 `/login`
- 同目录下还有一个 `loading.tsx`，目前只返回 `null`

### 顶部导航（Header）

- 使用 `<header>` 包裹
- 包含一个返回首页的"后退"按钮（`<ArrowLeft>` + `<Link href="/">`）
- 项目 Logo 组件 `<Logo />`
- 样式通过 Tailwind CSS 及 Button 组件（shadcn/ui）控制外观

### 登录/注册切换（Tabs）

- 整体由 shadcn/ui 的 `<Tabs>`、`<TabsList>`、`<TabsTrigger>`、`<TabsContent>` 组成
- `defaultValue="login"`，两个触发器分别为"登录"和"注册"

### 表单结构

#### 登录表单 (`<TabsContent value="login">`)

- `<Card>` 容器 + `<CardHeader>`（标题和描述）
- `<CardContent>`：两个输入框（邮箱、密码），密码区块内有"忘记密码"链接按钮
- `<CardFooter>`：全宽提交按钮
- 提交时调用 `handleLogin`，内部通过 `useState` 管理 `isLoading`，模拟异步（`setTimeout`），完成后用 `router.push(redirectUrl)` 跳转

#### 注册表单 (`<TabsContent value="register">`)

- 结构类似，只是字段变为用户名、邮箱、密码
- 提交时调用 `handleRegister`，同样模拟异步并跳转

### 状态与导航

- `useState` 管理加载状态
- `useRouter`/`useSearchParams` 拿到 URL 上的 `redirect` 参数，操作完成后跳回指定页面

## 二、with-supabase 示例项目中 Supabase 邮箱/密码认证的接入

### 项目结构概览

- `app/(auth-pages)/sign-in/page.tsx`：登录页面
- `app/(auth-pages)/sign-up/page.tsx`：注册页面
- `app/(auth-pages)/forgot-password/page.tsx`：忘记密码页面
- `app/protected/reset-password/page.tsx`：重置密码页面
- `app/auth/callback/route.ts`：OAuth 回调路由，用于 code→session 交换
- `app/actions.ts`：所有表单提交的 Server Actions
- `utils/supabase/client.ts` & `utils/supabase/server.ts`：封装 Supabase 客户端（浏览器/SSR）
- `utils/supabase/middleware.ts` + 根目录 `middleware.ts`：会话刷新和路由守卫
- `utils/utils.ts`：`encodedRedirect`，带消息的跳转辅助函数

### Supabase 客户端封装

#### 浏览器端

```typescript
// utils/supabase/client.ts
import { createBrowserClient } from "@supabase/ssr";
```

#### 服务端（SSR/Server Actions/路由守卫）

```typescript
// utils/supabase/server.ts
import { createServerClient } from "@supabase/ssr";
```

### Server Actions（app/actions.ts）

- `signUpAction(formData: FormData)` → `supabase.auth.signUp({ email, password, options: { emailRedirectTo } })`
- `signInAction(formData)` → `supabase.auth.signInWithPassword({ email, password })`
- `forgotPasswordAction(formData)` → `supabase.auth.resetPasswordForEmail(...)`
- `resetPasswordAction(formData)` → `supabase.auth.updateUser({ password })`
- `signOutAction()` → `supabase.auth.signOut()`

这些 Action 大多通过 `encodedRedirect(type, path, message)` 将结果（成功/失败消息）附加到查询参数后跳转，或直接调用 `redirect()`。

### OAuth 回调与会话管理

#### OAuth 回调

- `app/auth/callback/route.ts`：接收 `?code=`，调用 `supabase.auth.exchangeCodeForSession(code)`，写入 cookie，然后重定向到原地址或受保护页面

#### 中间件

`middleware.ts` → 调用 `updateSession(request)`：

- 通过 `createServerClient` 同步 Cookie
- 调用 `supabase.auth.getUser()` 刷新会话
- 根据登录状态对 `/protected` 路径做路由守卫（未登录跳 `/sign-in`；已登录访问根 `/` 则跳 `/protected`）

### 前端页面如何消耗这些 Action

- 登录、注册、重置密码等页面都是 Server Components（`async function`）
- 内部使用 Next.js 的 `<form action={signInAction}>` 或显式的 `formAction`
- 提交后，页面会自动接收 `?success=` 或 `?error=` 查询参数，通过封装好的 `FormMessage` 组件展示给用户

### 受保护页面示例

`app/protected/page.tsx`：

- 直接在 Server Component 里 `await createClient()`
- 再 `await supabase.auth.getUser()`
- 如果没有 `user`，则 `redirect("/sign-in")`
- 否则展示用户信息

## 总结

### AI Tools Navigation 现状

- 目前只做了前端模拟登录/注册
- 结构上依赖 shadcn/ui 的 Tabs + Card + 表单 + Next.js Client Component

### with-supabase 示例项目特点

完整接入了 Supabase Auth，通过：

- SSR 与浏览器端两套 client
- Server Actions 统一处理 auth 流程
- 中间件保持会话同步并做路由守卫
- OAuth callback 交换与重定向

实现了真正的邮箱/密码登录、注册、忘记密码、重置密码及登出功能。
