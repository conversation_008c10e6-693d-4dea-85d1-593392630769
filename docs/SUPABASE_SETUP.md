# Supabase 设置指南

## 1. 创建 Supabase 项目

1. 访问 [Supabase](https://supabase.com) 并创建账号
2. 点击 "New Project" 创建新项目
3. 填写项目名称、数据库密码等信息
4. 等待项目创建完成

## 2. 获取项目配置

在项目仪表板中，找到以下信息：

1. **Project URL**: 在 Settings > API 中找到 `URL`
2. **Anon Key**: 在 Settings > API 中找到 `anon` `public` key
3. **Service Role Key**: 在 Settings > API 中找到 `service_role` `secret` key

## 3. 配置环境变量

在项目根目录创建 `.env.local` 文件：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## 4. 配置认证设置

在 Supabase 仪表板中：

1. 进入 Authentication > Settings
2. 在 Site URL 中添加你的域名（开发环境：`http://localhost:3000`）
3. 在 Redirect URLs 中添加回调地址：
   - `http://localhost:3000/auth/callback` (开发环境)
   - `https://yourdomain.com/auth/callback` (生产环境)

## 5. 启用邮箱认证

1. 在 Authentication > Providers 中
2. 确保 Email 提供商已启用
3. 配置邮件模板（可选）

## 6. 配置自定义 SMTP（推荐）

**重要**：Supabase 内置邮件服务有严格限制（每小时 3-4 封），仅用于演示。生产环境必须配置自定义 SMTP。

### 使用 Resend（推荐）

1. **注册 Resend 账号**

   - 访问 [Resend](https://resend.com)
   - 创建免费账号（每月 3000 封邮件免费额度）

2. **获取 API Key**

   - 在 Resend 仪表板中创建 API Key
   - 选择 "Full access" 权限

3. **在 Supabase 中配置 SMTP**

   - 进入 Authentication > Settings
   - 滚动到 "SMTP Settings" 部分
   - 填写以下信息：
     ```
     SMTP Host: smtp.resend.com
     SMTP Port: 587
     SMTP Username: resend
     SMTP Password: [你的 Resend API Key]
     Sender Email: <EMAIL>
     Sender Name: Your App Name
     ```

4. **验证域名（可选但推荐）**
   - 在 Resend 中添加你的域名
   - 按照指示配置 DNS 记录
   - 验证域名后可以使用自定义发件人地址

### 其他 SMTP 提供商选项

- **SendGrid**: 每月 100 封免费
- **Mailgun**: 每月 5000 封免费（前 3 个月）
- **Amazon SES**: 按使用量付费
- **Postmark**: 每月 100 封免费

## 7. 测试连接

启动开发服务器：

```bash
pnpm dev
```

访问 `/login` 页面测试注册和登录功能。

## 常见问题

### 1. 邮件未收到

**可能原因**：

- 使用 Supabase 内置 SMTP（限制每小时 3-4 封）
- 邮件被垃圾邮件过滤器拦截
- SMTP 配置错误
- 域名未验证

**排查步骤**：

1. 检查垃圾邮件文件夹
2. 在 Supabase 仪表板 Authentication > Users 中查看用户状态
3. 查看 Supabase 项目日志（Logs > Auth）
4. 如果使用自定义 SMTP，检查提供商的发送日志
5. 配置自定义 SMTP 提供商（如 Resend）

**立即解决方案**：

- 配置 Resend 或其他 SMTP 提供商
- 在开发环境中可以手动在 Supabase 仪表板中确认用户邮箱

### 2. 回调错误

- 确认 Redirect URLs 配置正确
- 检查环境变量是否正确设置
- 确认域名和端口号匹配

### 3. 认证失败

- 检查 API Keys 是否正确
- 确认项目 URL 格式正确
- 查看浏览器控制台和网络请求错误信息
