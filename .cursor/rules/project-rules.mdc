---
description: 
globs: 
alwaysApply: true
---
# AI工具导航网站 - 开发规范文档
## 项目概述

### 项目名称

AI工具导航网站 (AI Tools Navigation Website)

### 技术栈

- **前端框架**: Next.js 15 (App Router)
- **样式**: Tailwind CSS + shadcn/ui
- **数据库**: Supabase (PostgreSQL)
- **部署**: Vercel
- **语言**: TypeScript
- **状态管理**: React Hooks (useState, useEffect)


## 项目结构规范

### 目录结构

```plaintext
├── app/                    # Next.js App Router 页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── login/             # 登录页面
│   ├── search/            # 搜索页面
│   ├── tool/[slug]/       # 工具详情页
│   └── account/           # 个人中心
├── components/            # 可复用组件
│   ├── ui/               # shadcn/ui 基础组件
│   ├── category-sidebar.tsx
│   ├── search-bar.tsx
│   ├── tool-card.tsx
│   └── ...
├── lib/                  # 工具函数和配置
│   ├── utils.ts          # 通用工具函数
│   └── mock-data.ts      # 模拟数据
└── hooks/                # 自定义 Hooks
```

## 代码规范

### 1. 文件命名

- **组件文件**: kebab-case (例: `search-bar.tsx`)
- **页面文件**: kebab-case (例: `[slug]/page.tsx`)
- **工具函数**: camelCase (例: `formatDate`)


### 2. 组件规范

#### 组件结构

```typescriptreact
"use client" // 仅客户端组件需要

import type React from "react"
import { useState } from "react"
import { Button } from "@/components/ui/button"

interface ComponentProps {
  title: string
  isActive?: boolean
}

export function ComponentName({ title, isActive = false }: ComponentProps) {
  const [state, setState] = useState(false)

  const handleClick = () => {
    // 处理逻辑
  }

  return (
    <div className="component-container">
      {/* JSX 内容 */}
    </div>
  )
}
```

#### 组件命名

- 使用 PascalCase
- 导出使用 `export function` 而非 `export default`
- Props 接口命名: `ComponentNameProps`


### 3. 样式规范

#### Tailwind CSS 使用

```typescriptreact
// ✅ 推荐：使用 cn 函数合并类名
import { cn } from "@/lib/utils"

<Button 
  className={cn(
    "base-styles",
    isActive && "active-styles",
    "additional-styles"
  )}
>
```

#### 颜色规范

- **主色调**: 蓝色 (`blue-500`, `blue-600`)
- **背景色**: 白色/灰色系 (`bg-white`, `bg-gray-50`)
- **文字色**: 灰色系 (`text-gray-900`, `text-gray-600`)
- **边框色**: 灰色系 (`border-gray-200`, `border-gray-300`)


### 4. 状态管理规范

#### useState 使用

```typescriptreact
// ✅ 推荐：明确的状态类型
const [isLoading, setIsLoading] = useState<boolean>(false)
const [user, setUser] = useState<User | null>(null)

// ✅ 推荐：状态更新函数命名
const handleSubmit = async () => {
  setIsLoading(true)
  // 处理逻辑
  setIsLoading(false)
}
```

## 设计规范

### 1. 设计风格

- **设计语言**: Apple Design 风格
- **特点**: 简洁、清新、科技感
- **布局**: 扁平化设计，参考 Ant Design 后台风格


### 2. 布局规范

#### 页面布局

```plaintext
┌─────────────────────────────────────┐
│           顶部导航栏                  │ ← 固定，包含 Logo、搜索、登录
├─────────────┬───────────────────────┤
│   左侧菜单   │      右侧内容区        │
│   (分类)    │    (工具列表/详情)     │
│            │                      │
└─────────────┴───────────────────────┘
```

#### 个人中心布局

```plaintext
┌─────────────────────────────────────┐
│           顶部导航栏                  │
├─────────────┬───────────────────────┤
│   左侧菜单   │      标题栏            │
│  (功能菜单)  ├───────────────────────┤
│            │      内容区域          │
│            │   (无卡片样式)         │
└─────────────┴───────────────────────┘
```

### 3. 组件规范

#### 工具卡片

- 无边框卡片样式
- 悬停效果：轻微上移 + 阴影
- 文字描述：单行显示，超出省略


#### 表格样式

- 无卡片边框
- 斑马纹背景
- 悬停高亮行


## 数据库规范 (Supabase)

### 表结构设计

#### 用户表 (users)

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 工具表 (tools)

```sql
CREATE TABLE tools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  url TEXT NOT NULL,
  logo_url TEXT,
  cover_url TEXT,
  category VARCHAR(50) NOT NULL,
  summary TEXT NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- pending, published, rejected
  is_popular BOOLEAN DEFAULT FALSE,
  is_new BOOLEAN DEFAULT TRUE,
  submitted_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 数据操作规范

#### 查询示例

```typescript
// ✅ 推荐：使用 TypeScript 类型
interface Tool {
  id: string
  name: string
  slug: string
  // ...其他字段
}

// 获取工具列表
const { data: tools, error } = await supabase
  .from('tools')
  .select('*')
  .eq('status', 'published')
  .order('created_at', { ascending: false })
```

## 路由规范

### 页面路由

- `/` - 首页
- `/search?q=keyword` - 搜索页面
- `/tool/[slug]` - 工具详情页
- `/login` - 登录页面
- `/account?tab=history` - 个人中心


### 导航规范

- 所有页面都有统一的顶部导航栏
- 登录页面简化导航（无搜索和登录按钮）
- 个人中心有返回首页功能


## 性能规范

### 1. 图片优化

- 使用 Next.js Image 组件
- 占位图使用 `/placeholder.svg?height=x&width=y&query=desc`
- Logo 推荐尺寸: 512x512px
- 预览图推荐尺寸: 1200x600px


### 2. 代码分割

- 页面级别自动代码分割
- 大型组件使用动态导入


### 3. SEO 优化

- 每个页面设置合适的 metadata
- 使用语义化 HTML 标签


## 测试规范

### 组件测试

- 关键交互功能需要测试
- 表单提交流程测试
- 搜索功能测试


## 部署规范

### 环境变量

```plaintext
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 部署流程

1. 代码推送到 GitHub
2. Vercel 自动部署
3. 环境变量在 Vercel 项目设置中配置


## 开发工作流

### 1. 功能开发流程

1. 创建功能分支
2. 开发组件/页面
3. 测试功能
4. 代码审查
5. 合并到主分支


### 2. 代码提交规范

```plaintext
feat: 添加用户认证功能
fix: 修复搜索框样式问题
style: 优化个人中心布局
docs: 更新开发规范文档
```

### 3. 分支命名

- `feature/user-auth` - 功能分支
- `fix/search-styling` - 修复分支
- `hotfix/critical-bug` - 紧急修复


这份开发规范涵盖了项目的主要方面，可以根据项目发展需要进行调整和补充。