---
description: 
globs: 
alwaysApply: true
---
# 代码风格规范

## **1. 基本原则**
- 保持代码简洁、一致且可读
- 使用统一的缩进和格式
- 避免过长的函数和文件
- 遵循"干净代码"原则

## 2. 格式化
### 缩进和空格
- 使用2个或4个空格进行缩进（项目内保持一致）
- 不使用制表符（Tab）
- 运算符两侧添加空格
- 逗号后添加空格

### 换行
- 每行代码长度不超过80-120个字符（根据团队约定）
- 链式调用应换行并对齐
- 函数参数过多时进行换行
- 长表达式应适当换行增加可读性

### 括号和空行
- 控制语句的左花括号与声明在同一行
- 添加适当的空行分隔代码逻辑段
- 保持括号匹配和对齐
- 避免多余的括号和空行

## 3. 命名约定
### 通用原则
- 名称应清晰表达意图，避免缩写（除了广泛接受的缩写）
- 名称长度应与作用域大小成正比
- 避免使用单字母变量名（除了临时变量如循环计数器）
- 避免使用无意义的名称（如 temp, foo, bar）

### 命名风格
- 类/接口：使用大驼峰命名法（PascalCase）
  ```
  class UserService {}
  interface DatabaseConnection {}
  ```
  
- 变量/函数：使用小驼峰命名法（camelCase）或蛇形命名法（snake_case）（根据语言惯例）
  ```
  let userData = {};
  function calculateTotal() {}
  ```
  
- 常量：使用大写蛇形命名法（UPPER_SNAKE_CASE）
  ```
  const MAX_RETRY_COUNT = 3;
  const API_BASE_URL = 'https://api.example.com';
  ```

## 4. 注释规范
### 代码注释
- 使用 JSDoc, JavaDoc 或语言对应的注释格式
- 每个函数、类和复杂代码块添加注释
- 注释应解释"为什么"而不仅仅是"做什么"
- 及时更新注释，避免过时

### 行内注释
- 复杂逻辑处添加行内注释
- 保持简洁，避免冗余
- 使用统一的注释前缀标记特殊情况：

## 代码组织
### 文件结构
- 一个文件只包含一个主要类/组件/模块
- 相关代码组织在一起
- 按照功能进行文件分组，而不是按类型
- 导入顺序：标准库 > 第三方库 > 项目内模块

### 代码块组织
- 相关功能的代码应放在一起
- 从抽象到具体，从高层到低层
- 公有方法在前，私有方法在后
- 按照逻辑分组代码，而不是按字母顺序

## 工具支持
- 使用 ESLint, Prettier, EditorConfig 等工具强制代码风格
- 提供适用于常见编辑器的配置文件
- 在 CI 流程中检查代码风格
- 使用 Git Hooks (如 husky) 在提交前自动格式化代码

## 最佳实践
- 定期代码审查确保风格一致性
- 风格问题不应阻碍开发进度，但应在代码审查中解决
- 风格规范可随项目发展进行调整，但需要达成共识 