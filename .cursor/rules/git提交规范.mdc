---
description: git 提交
globs: 
alwaysApply: false
---
# Git 提交规范 (Git Commit Rules)

## 1. 提交信息格式
### 1.1 基本格式
```
<type>(<scope>): <subject>
<body>
<footer>
```
### 类型（Type）
- feat: 新功能
- fix: 修复bug
- docs: 文档更改
- style: 代码格式调整
- refactor: 重构代码
- perf: 性能优化
- test: 测试相关
- chore: 构建过程或辅助工具变动

### 主题（Subject）
- 简短描述，不超过50字符
- 使用现在时态
- 首字母不大写，结尾不加句号

## 2. 分支管理核心
- main/master: 主分支，稳定版本
- develop: 开发分支
- feature/*: 功能分支
- bugfix/*: 修复分支
- hotfix/*: 紧急修复分支
- release/*: 发布分支

## 3. 提交最佳实践
### 原子提交
- 每次提交只做一件事
- 相关改动放在同一提交
- 不相关改动分开提交

### 提交频率
- 经常小批量提交
- 功能完成时提交
- 解决冲突后提交

### 提交前准备
- 拉取最新代码
- 确保测试通过
- 检查代码风格
- 更新必要文档

### 安全考虑
- 不提交敏感信息
- 使用.gitignore忽略不必要文件
- 保护主分支

## 4. 版本管理
- 遵循语义化版本：主版本号.次版本号.修订号
- 重要发布打标签：`git tag -a v1.0.0 -m "version 1.0.0"`

## 5. 代码审查
- 使用Pull Request进行代码审查
- 解决所有评论后再合并
- 合并后删除临时分支