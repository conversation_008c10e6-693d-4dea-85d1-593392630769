---
description: 写文档（README.md、CHANGELOG.md等.md文档）
globs: 
alwaysApply: false
---
# 文档撰写规范

## 1. 文件结构规范
### 必需文件
项目中必须包含以下Markdown文档：
- `README.md` - 项目主文档
- `CHANGELOG.md` - 版本更新日志
- 其他相关的 `.md` 文档

### README.md 规范
1. **文档结构**
   - 保持文档结构清晰，使用适当的Markdown标记
   - **重要**: 每次修改必须保留 README.md 中的二级目录"Cursor 历史下载链接"部分，不要进行删除

2. **必需章节**
   README.md 必须包含以下部分：
   - 项目简介
   - 安装说明
   - 使用方法
   - 贡献指南（如适用）
   - 许可证信息
   - Cursor 历史下载链接（必须保留）

### CHANGELOG.md 规范
1. **版本号格式**
   使用语义化版本号（如：v1.0.0）

2. **更新内容格式**
   ```markdown
   ## v1.0.0
   
   - 新增功能：[功能描述]
   - 修复bug：[bug描述]
   ```

3. **分类标签**
   使用以下标签分类更新内容：
   - 新增功能
   - 修复bug
   - 改进优化
   - 破坏性变更

## 2. 文档更新原则
1. **同步更新**
   - 保持文档与代码同步更新
   - 文档变更必须与代码变更一起提交

2. **语言规范**
   - 使用简洁明了的语言
   - 避免使用晦涩难懂的专业术语
   - 必要时提供术语解释

3. **示例规范**
   - 提供足够的示例和说明
   - 示例代码必须经过测试验证
   - 示例应当简单易懂，突出重点

4. **格式统一**
   - 确保文档格式一致
   - 使用统一的标题层级
   - 保持列表格式统一
   - 代码块使用适当的语言标记

## 3. Markdown 格式规范
1. **标题使用**
   - 一级标题 (#) 用于文档标题
   - 二级标题 (##) 用于主要章节
   - 三级及以下标题用于子章节
   - 标题层级不应超过四级

2. **列表格式**
   - 无序列表使用 `-` 
   - 有序列表使用 `1.` 
   - 保持列表项缩进一致

3. **代码块**
   ```
   - 使用三个反引号包裹代码
   - 指定代码语言类型
   - 保持代码格式化
   ```

4. **强调语法**
   - 使用 `**文本**` 进行加粗强调
   - 使用 `*文本*` 进行斜体标记
   - 重要信息使用加粗标记

## 4. 文档维护
1. **定期审查**
   - 定期检查文档的准确性
   - 更新过时的信息
   - 补充必要的新内容

2. **版本控制**
   - 文档变更要有版本记录
   - 重大变更需要在CHANGELOG中记录
   - 保留历史版本信息

## 4. 贡献指南
1. **文档贡献**
   - 遵循现有的文档格式
   - 提供清晰的变更说明
   - 确保文档更新经过审核

2. **质量控制**
   - 提交前进行拼写和语法检查
   - 确保链接可用
   - 验证示例代码的正确性 