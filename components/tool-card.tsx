import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface ToolCardProps {
  id: string
  name: string
  description: string
  logo: string
  category: string
  slug: string
  className?: string
}

export function ToolCard({ id, name, description, logo, category, slug, className }: ToolCardProps) {
  return (
    <Link href={`/tool/${slug}`} target="_blank" className={cn("block", className)}>
      <Card className="h-full overflow-hidden tool-card border-0 bg-card/50 hover:bg-card">
        <CardContent className="p-4 flex items-start gap-3">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-secondary flex items-center justify-center">
              <Image src={logo || "/placeholder.svg"} alt={name} width={48} height={48} className="object-cover" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-base mb-1 truncate">{name}</h3>
            <p className="text-sm text-muted-foreground truncate">{description}</p>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
