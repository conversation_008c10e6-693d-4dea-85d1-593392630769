"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  PenLine,
  ImageIcon,
  Video,
  Briefcase,
  Paintbrush,
  MessageSquare,
  Code,
  Search,
  Music,
  Layers,
  BookIcon as BookTranslation,
  Brain,
  Scale,
  GraduationCap,
  Loader2,
} from "lucide-react";
import { getIconComponent } from "@/lib/icon-mapping";
import type { Category, CategoryWithIcon } from "@/types/category";

export function CategorySidebar() {
  const [activeCategory, setActiveCategory] = useState<string>("ai-writing");
  const [categories, setCategories] = useState<CategoryWithIcon[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await fetch("/api/categories");

        if (!response.ok) {
          throw new Error("Failed to fetch categories");
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        // 将分类数据转换为带图标的格式
        const categoriesWithIcons: CategoryWithIcon[] = data.categories.map(
          (category: Category) => {
            const IconComponent = getIconComponent(category.icon_name || null);
            return {
              ...category,
              icon: <IconComponent className="h-5 w-5" />,
            };
          }
        );

        setCategories(categoriesWithIcons);

        // 设置默认激活分类为第一个分类
        if (categoriesWithIcons.length > 0) {
          setActiveCategory(categoriesWithIcons[0].slug);
        }
      } catch (err) {
        console.error("Error fetching categories:", err);
        setError(err instanceof Error ? err.message : "Unknown error");
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const scrollToCategory = (categorySlug: string) => {
    const element = document.getElementById(categorySlug);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setActiveCategory(categorySlug);
    }
  };

  return (
    <aside className="fixed left-0 top-12 bottom-0 w-64 bg-secondary/30 overflow-y-auto hidden md:block z-20">
      <div className="p-6">
        <div className="space-y-1">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">
                加载分类中...
              </span>
            </div>
          ) : error ? (
            <div className="py-4 text-center">
              <p className="text-sm text-red-500">加载分类失败</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          ) : (
            categories.map((category) => (
              <Button
                key={category.id}
                variant="ghost"
                className={cn(
                  "w-full justify-start",
                  activeCategory === category.slug &&
                    "bg-primary/10 text-primary"
                )}
                onClick={() => scrollToCategory(category.slug)}
              >
                <span className="mr-2">{category.icon}</span>
                {category.name}
              </Button>
            ))
          )}
        </div>
      </div>
    </aside>
  );
}
