"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import { User } from "lucide-react"

export function UserProfile() {
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  // Mock user data
  const [user, setUser] = useState({
    name: "张三",
    email: "zhang<PERSON>@example.com",
    avatar: "/placeholder.svg?height=100&width=100&query=user",
  })

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsUpdating(true)

    // Simulate profile update
    setTimeout(() => {
      setIsUpdating(false)
      toast({
        title: "更新成功",
        description: "您的个人信息已更新",
      })
    }, 1500)
  }

  return (
    <div className="max-w-2xl">
      <div className="bg-white p-6">
        <form onSubmit={handleUpdateProfile} className="space-y-6">
          <div className="flex flex-col items-center space-y-4">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
              <AvatarFallback>
                <User className="h-12 w-12" />
              </AvatarFallback>
            </Avatar>
            <Button variant="outline" size="sm">
              更换头像
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name">用户名</Label>
              <Input id="name" value={user.name} onChange={(e) => setUser({ ...user, name: e.target.value })} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                value={user.email}
                onChange={(e) => setUser({ ...user, email: e.target.value })}
                disabled
              />
              <p className="text-xs text-muted-foreground">邮箱地址不可更改</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <Input id="password" type="password" placeholder="••••••••" />
            <p className="text-xs text-muted-foreground">留空表示不修改密码</p>
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "更新中..." : "更新个人信息"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
