"use client";

import { ToolCard } from "@/components/tool-card";
import { useState, useEffect } from "react";
import type { Tool } from "@/types/tool";

interface ToolsGridProps {
  category: string;
  limit?: number;
}

export function ToolsGrid({ category, limit }: ToolsGridProps) {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setLoading(true);
        setError(null);

        // 构建查询参数
        const params = new URLSearchParams();
        params.append("category", category);
        if (limit) {
          params.append("limit", limit.toString());
        }

        const response = await fetch(`/api/tools?${params.toString()}`);

        if (!response.ok) {
          throw new Error("Failed to fetch tools");
        }

        const data = await response.json();
        setTools(data.tools || []);
      } catch (err) {
        console.error("Error fetching tools:", err);
        setError("加载工具数据失败，请稍后重试");
      } finally {
        setLoading(false);
      }
    };

    fetchTools();
  }, [category, limit]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* 加载骨架屏 */}
        {Array.from({ length: limit || 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-24 bg-muted rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-primary hover:text-primary/80 underline"
        >
          重新加载
        </button>
      </div>
    );
  }

  if (tools.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">暂无工具数据</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {tools.map((tool) => (
        <ToolCard
          key={tool.id}
          id={tool.id}
          name={tool.name}
          description={tool.summary || "暂无描述"}
          logo={tool.logo_url || "/placeholder.svg"}
          category={tool.category?.slug || ""}
          slug={tool.slug}
        />
      ))}
    </div>
  );
}
