"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, LogOut } from "lucide-react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { signOutAction } from "@/app/actions";
import type { User as SupabaseUser } from "@supabase/supabase-js";

export function AuthButton() {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    setMounted(true);

    // 获取当前用户状态
    const getUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user);
      setLoading(false);
    };

    getUser();

    // 监听认证状态变化
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth]);

  const handleLogin = () => {
    router.push("/login");
  };

  const handleLogout = async () => {
    await signOutAction();
  };

  const navigateToAccount = (tab?: string) => {
    router.push(`/account${tab ? `?tab=${tab}` : ""}`);
  };

  // 在组件挂载前不渲染，避免 hydration 错误
  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="text-sm font-medium"
        disabled
      >
        加载中...
      </Button>
    );
  }

  // 加载状态时显示占位符
  if (loading) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="text-sm font-medium"
        disabled
      >
        加载中...
      </Button>
    );
  }

  // 未登录状态
  if (!user) {
    return (
      <Button
        onClick={handleLogin}
        variant="outline"
        size="sm"
        className="text-sm font-medium"
      >
        登录
      </Button>
    );
  }

  // 已登录状态 - 显示个人中心下拉菜单
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="text-sm font-medium">
          <User className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <div className="px-4 py-3 border-b">
          <div className="font-semibold text-sm text-gray-900">
            {user.user_metadata?.name || "用户"}
          </div>
          <div className="text-sm text-gray-500">{user.email}</div>
        </div>
        <DropdownMenuItem
          onClick={() => navigateToAccount()}
          className="px-4 py-3"
        >
          <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
            个人中心
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleLogout}
          className="px-4 py-3 text-gray-700 hover:bg-gray-50"
        >
          <div className="flex items-center justify-between w-full">
            <span className="text-sm font-medium uppercase tracking-wide">
              退出登录
            </span>
            <LogOut className="h-4 w-4" />
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
