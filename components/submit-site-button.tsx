"use client";

import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";

export function SubmitSiteButton() {
  const [user, setUser] = useState<User | null>(null);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);

    const supabase = createClient();

    // 获取当前用户
    const getUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();

    // 监听认证状态变化
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSubmitSite = () => {
    if (user) {
      // 已登录，直接跳转到个人中心的提交表单页面
      router.push("/account?tab=submit");
    } else {
      // 未登录，跳转到登录页面，登录成功后重定向到提交表单
      router.push("/login?redirect=/account?tab=submit");
    }
  };

  // 在组件挂载前不渲染，避免 hydration 错误
  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="gap-2 text-sm font-medium"
        disabled
      >
        <Plus className="h-4 w-4" />
        提交站点
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      size="sm"
      className="gap-2 text-sm font-medium"
      onClick={handleSubmitSite}
    >
      <Plus className="h-4 w-4" />
      提交站点
    </Button>
  );
}
