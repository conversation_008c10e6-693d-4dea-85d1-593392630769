"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

// 定义提交记录的类型
interface ToolSubmission {
  id: string;
  name: string;
  slug: string;
  url: string;
  logo_url?: string;
  cover_url?: string;
  category_id: string;
  summary: string;
  description: string;
  faqs?: string;
  submitted_at: string;
  status: "pending" | "approved" | "rejected";
  category?: {
    id: string;
    name: string;
    slug: string;
  };
}

export function SubmissionHistory() {
  const [selectedSubmission, setSelectedSubmission] =
    useState<ToolSubmission | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [submissions, setSubmissions] = useState<ToolSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取提交历史数据
  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/tool_submissions");

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error("请先登录");
          }
          throw new Error("获取数据失败");
        }

        const data = await response.json();
        setSubmissions(data.submissions || []);
      } catch (error) {
        console.error("Failed to fetch submissions:", error);
        setError(error instanceof Error ? error.message : "获取数据失败");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubmissions();
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleViewDetails = (submission: ToolSubmission) => {
    setSelectedSubmission(submission);
    setIsDialogOpen(true);
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "审核中";
      case "approved":
        return "已通过";
      case "rejected":
        return "已拒绝";
      default:
        return status;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "pending":
        return "outline" as const;
      case "approved":
        return "default" as const;
      case "rejected":
        return "destructive" as const;
      default:
        return "outline" as const;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 text-gray-500">
          正在加载提交历史...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <p className="text-gray-600">共 {submissions.length} 个提交记录</p>
      </div>

      {submissions.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          您还没有提交过任何工具
        </div>
      ) : (
        <div className="bg-white">
          <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-200 bg-gray-50 font-medium text-sm text-gray-700">
            <div>工具名称</div>
            <div>提交时间</div>
            <div>状态</div>
            <div>操作</div>
          </div>

          {submissions.map((submission, index) => (
            <div
              key={submission.id}
              className={`grid grid-cols-4 gap-4 p-4 hover:bg-gray-50 ${
                index !== submissions.length - 1
                  ? "border-b border-gray-100"
                  : ""
              }`}
            >
              <div className="font-medium text-gray-900">{submission.name}</div>
              <div className="text-gray-600 text-sm">
                {formatDate(submission.submitted_at)}
              </div>
              <div>
                <Badge variant={getStatusVariant(submission.status)}>
                  {getStatusText(submission.status)}
                </Badge>
              </div>
              <div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  onClick={() => handleViewDetails(submission)}
                >
                  查看详情
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-gray-900">
              工具详情
            </DialogTitle>
          </DialogHeader>

          {selectedSubmission && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    网站名称
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                    {selectedSubmission.name}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    网站Slug
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                    {selectedSubmission.slug}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  网站URL
                </Label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                  {selectedSubmission.url}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    网站Logo
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                    {selectedSubmission.logo_url ? (
                      <img
                        src={selectedSubmission.logo_url}
                        alt={`${selectedSubmission.name} Logo`}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500 text-sm">
                        无Logo
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    网站预览图
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                    {selectedSubmission.cover_url ? (
                      <img
                        src={selectedSubmission.cover_url}
                        alt={`${selectedSubmission.name} 预览图`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-24 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500 text-sm">
                        无预览图
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  工具分类
                </Label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                  {selectedSubmission.category?.name || "未知分类"}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  工具概要
                </Label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                  {selectedSubmission.summary}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  工具详细介绍
                </Label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900 min-h-32 whitespace-pre-wrap">
                  {selectedSubmission.description}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  工具常见问题
                </Label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900 min-h-32 whitespace-pre-wrap">
                  {selectedSubmission.faqs || "暂无常见问题"}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    提交时间
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900">
                    {formatDate(selectedSubmission.submitted_at)}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    审核状态
                  </Label>
                  <div className="px-3 py-2">
                    <Badge
                      variant={getStatusVariant(selectedSubmission.status)}
                    >
                      {getStatusText(selectedSubmission.status)}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
