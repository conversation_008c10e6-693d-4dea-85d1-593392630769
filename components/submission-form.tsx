"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Upload, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/lib/supabase/client";

interface Category {
  id: string;
  name: string;
  slug: string;
}

export function SubmissionForm() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [coverPreview, setCoverPreview] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [coverFile, setCoverFile] = useState<File | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  const supabase = createClient();

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error("Failed to fetch categories:", error);
      }
    };

    fetchCategories();
  }, []);

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setCoverFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setCoverPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 上传文件到 Supabase Storage
  const uploadFile = async (
    file: File,
    bucket: string,
    toolId: string,
    type: "logo" | "cover"
  ) => {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:-]/g, "")
      .split(".")[0];
    const ext = file.name.split(".").pop();
    const path = `${toolId}/${type}-${timestamp}.${ext}`;

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) {
      throw error;
    }

    // 获取公开 URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(path);

    return publicUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log("开始提交表单...");
      const formData = new FormData(e.target as HTMLFormElement);

      // 验证必填字段
      const name = formData.get("name") as string;
      const slug = formData.get("slug") as string;
      const url = formData.get("url") as string;
      const summary = formData.get("summary") as string;
      const description = formData.get("description") as string;

      if (
        !name ||
        !slug ||
        !url ||
        !selectedCategory ||
        !summary ||
        !description
      ) {
        throw new Error("请填写所有必填字段");
      }

      console.log("表单数据验证通过，开始上传文件...");

      // 生成临时 ID 用于文件上传路径
      const tempId = crypto.randomUUID();

      let logoUrl = "";
      let coverUrl = "";

      // 上传 Logo
      if (logoFile) {
        console.log("正在上传 Logo...");
        logoUrl = await uploadFile(logoFile, "tool-logos", tempId, "logo");
        console.log("Logo 上传成功:", logoUrl);
      }

      // 上传封面图
      if (coverFile) {
        console.log("正在上传封面图...");
        coverUrl = await uploadFile(coverFile, "tool-covers", tempId, "cover");
        console.log("封面图上传成功:", coverUrl);
      }

      // 准备提交数据
      const submissionData = {
        name,
        slug,
        url,
        category_id: selectedCategory,
        summary,
        description,
        faqs: (formData.get("faqs") as string) || "",
        logo_url: logoUrl,
        cover_url: coverUrl,
      };

      console.log("准备提交数据:", submissionData);

      // 调用 API 提交数据
      const response = await fetch("/api/tool_submissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submissionData),
      });

      console.log("API 响应状态:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API 错误响应:", errorData);
        throw new Error(errorData.error || "提交失败");
      }

      const result = await response.json();
      console.log("提交成功，返回数据:", result);

      // 提交成功
      toast({
        title: "提交成功",
        description: "您的工具已提交，正在等待审核",
      });

      console.log("Toast 通知已发送");

      // 重置表单
      const form = e.target as HTMLFormElement;
      form.reset();
      setLogoPreview(null);
      setCoverPreview(null);
      setLogoFile(null);
      setCoverFile(null);
      setSelectedCategory("");

      console.log("表单已重置");
    } catch (error) {
      console.error("提交错误:", error);
      toast({
        title: "提交失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      console.log("提交流程结束");
    }
  };

  return (
    <div className="max-w-2xl">
      <div className="bg-white p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name">网站名称</Label>
              <Input id="name" name="name" required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">网站Slug</Label>
              <Input id="slug" name="slug" required placeholder="my-ai-tool" />
              <p className="text-xs text-muted-foreground">
                用于URL，只能包含小写字母、数字和连字符
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">网站URL</Label>
            <Input
              id="url"
              name="url"
              type="url"
              required
              placeholder="https://"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">工具分类</Label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>网站Logo</Label>
            <div className="flex items-center gap-4">
              {logoPreview ? (
                <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-secondary">
                  <img
                    src={logoPreview || "/placeholder.svg"}
                    alt="Logo preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-0 right-0 bg-background/80 p-1 rounded-bl-lg"
                    onClick={() => {
                      setLogoPreview(null);
                      setLogoFile(null);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="w-16 h-16 rounded-lg bg-secondary flex items-center justify-center">
                  <Label
                    htmlFor="logo-upload"
                    className="cursor-pointer flex flex-col items-center justify-center text-muted-foreground"
                  >
                    <Upload className="h-6 w-6 mb-1" />
                    <span className="text-xs">上传</span>
                  </Label>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoChange}
                  />
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                推荐尺寸: 512x512px，格式: PNG, JPG
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>网站预览图</Label>
            <div className="flex items-center gap-4">
              {coverPreview ? (
                <div className="relative w-32 h-16 rounded-lg overflow-hidden bg-secondary">
                  <img
                    src={coverPreview || "/placeholder.svg"}
                    alt="Cover preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-0 right-0 bg-background/80 p-1 rounded-bl-lg"
                    onClick={() => {
                      setCoverPreview(null);
                      setCoverFile(null);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="w-32 h-16 rounded-lg bg-secondary flex items-center justify-center">
                  <Label
                    htmlFor="cover-upload"
                    className="cursor-pointer flex flex-col items-center justify-center text-muted-foreground"
                  >
                    <Upload className="h-6 w-6 mb-1" />
                    <span className="text-xs">上传</span>
                  </Label>
                  <Input
                    id="cover-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleCoverChange}
                  />
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                推荐尺寸: 1200x600px，格式: PNG, JPG
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="summary">工具概要</Label>
            <Input
              id="summary"
              name="summary"
              required
              placeholder="一句话描述该工具的主要功能"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">工具详细介绍</Label>
            <Textarea
              id="description"
              name="description"
              required
              placeholder="详细介绍该工具的功能、特点和使用场景"
              className="min-h-32"
            />
            <p className="text-xs text-muted-foreground">支持Markdown格式</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="faqs">常见问题</Label>
            <Textarea
              id="faqs"
              name="faqs"
              placeholder="列出用户可能遇到的常见问题及解答（可选）"
              className="min-h-24"
            />
            <p className="text-xs text-muted-foreground">
              支持Markdown格式，可选填写
            </p>
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "提交中..." : "提交工具"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
