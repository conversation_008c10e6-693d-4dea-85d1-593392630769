import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Logo } from "@/components/logo";
import { ArrowLeft, AlertCircle } from "lucide-react";
import Link from "next/link";

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex items-center py-2 px-6">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                返回首页
              </Button>
            </Link>
            <div className="h-6 w-px bg-border" />
            <Logo />
          </div>
        </div>
      </header>

      {/* Error content */}
      <div className="flex-1 flex items-center justify-center bg-secondary/30 p-4">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-red-600">认证失败</CardTitle>
              <CardDescription>
                很抱歉，在处理您的认证请求时出现了问题。这可能是由于链接已过期或无效。
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">请尝试以下解决方案：</p>
                <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                  <li>检查您的邮箱中是否有新的验证邮件</li>
                  <li>确保您点击的是最新的验证链接</li>
                  <li>重新注册或重置密码</li>
                </ul>
              </div>
              <div className="flex flex-col gap-2">
                <Link href="/login">
                  <Button className="w-full">返回登录页面</Button>
                </Link>
                <Link href="/">
                  <Button variant="outline" className="w-full">
                    返回首页
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
