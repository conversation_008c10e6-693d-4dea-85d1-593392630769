import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "AI Tools Collection",
  description: "Discover the best AI tools for your needs",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" suppressHydrationWarning data-oid="f08br2s">
      <body suppressHydrationWarning className="" data-oid="q7xhmff">
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
          data-oid="g-gdamu"
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
