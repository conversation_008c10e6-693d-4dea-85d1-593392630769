import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // 获取当前用户
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 查询当前用户的提交记录，包含分类信息
    const { data: submissions, error } = await supabase
      .from("tool_submissions")
      .select(
        `
        *,
        category:categories(id, name, slug)
      `
      )
      .eq("user_id", user.id)
      .order("submitted_at", { ascending: false });

    if (error) {
      console.error("Error fetching tool submissions:", error);
      return NextResponse.json(
        { error: "Failed to fetch submissions" },
        { status: 500 }
      );
    }

    return NextResponse.json({ submissions: submissions || [] });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // 获取当前用户
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const {
      name,
      slug,
      url,
      logo_url,
      cover_url,
      category_id,
      summary,
      description,
      faqs,
    } = body;

    // 基础字段验证
    if (!name || !slug || !url || !category_id) {
      return NextResponse.json(
        { error: "Missing required fields: name, slug, url, category_id" },
        { status: 400 }
      );
    }

    // 插入数据到 tool_submissions 表
    const { data: submission, error } = await supabase
      .from("tool_submissions")
      .insert({
        user_id: user.id,
        name,
        slug,
        url,
        logo_url,
        cover_url,
        category_id,
        summary,
        description,
        faqs,
        status: "pending", // 默认状态
        submitted_at: new Date().toISOString(),
      })
      .select(
        `
        *,
        category:categories(id, name, slug)
      `
      )
      .single();

    if (error) {
      console.error("Error creating tool submission:", error);
      return NextResponse.json(
        { error: "Failed to create submission" },
        { status: 500 }
      );
    }

    return NextResponse.json({ submission }, { status: 201 });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
