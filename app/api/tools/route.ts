import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const category = searchParams.get("category");
    const limit = searchParams.get("limit");
    const slug = searchParams.get("slug");

    // 如果是查询单个工具详情
    if (slug) {
      const { data: tool, error } = await supabase
        .from("tools")
        .select(
          `
          *,
          category:categories(id, name, slug)
        `
        )
        .eq("slug", slug)
        .eq("status", "published")
        .single();

      if (error) {
        console.error("Error fetching tool:", error);
        return NextResponse.json({ error: "Tool not found" }, { status: 404 });
      }

      return NextResponse.json({ tool });
    }

    // 构建查询
    let query = supabase
      .from("tools")
      .select(
        `
        id,
        name,
        slug,
        url,
        logo_url,
        summary,
        is_popular,
        is_new,
        category:categories(id, name, slug)
      `
      )
      .eq("status", "published");

    // 根据分类过滤
    if (category === "popular") {
      query = query.eq("is_popular", true);
    } else if (category === "new") {
      query = query.eq("is_new", true);
    } else if (category && category !== "all") {
      // 通过分类 slug 查询
      const { data: categoryData } = await supabase
        .from("categories")
        .select("id")
        .eq("slug", category)
        .single();

      if (categoryData) {
        query = query.eq("category_id", categoryData.id);
      }
    }

    // 排序：热门工具优先，然后按创建时间倒序
    query = query
      .order("is_popular", { ascending: false })
      .order("created_at", { ascending: false });

    // 限制数量
    if (limit) {
      const limitNum = parseInt(limit, 10);
      if (!isNaN(limitNum) && limitNum > 0) {
        query = query.limit(limitNum);
      }
    }

    const { data: tools, error } = await query;

    if (error) {
      console.error("Error fetching tools:", error);
      return NextResponse.json(
        { error: "Failed to fetch tools" },
        { status: 500 }
      );
    }

    return NextResponse.json({ tools: tools || [] });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
