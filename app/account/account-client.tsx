"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { SubmissionHistory } from "@/components/submission-history";
import { SubmissionForm } from "@/components/submission-form";
import { UserProfile } from "@/components/user-profile";
import { Logo } from "@/components/logo";
import { cn } from "@/lib/utils";
import { History, FileText, UserIcon, LogOut, Home } from "lucide-react";
import Link from "next/link";
import { signOutAction } from "@/app/actions";
import type { User } from "@supabase/supabase-js";

const menuItems = [
  { id: "history", label: "提交历史", icon: History },
  { id: "submit", label: "提交表单", icon: FileText },
  { id: "profile", label: "个人信息", icon: UserIcon },
];

interface AccountClientProps {
  user: User;
}

export function AccountClient({ user }: AccountClientProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("history");

  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab && ["history", "submit", "profile"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    router.push(`/account?tab=${tabId}`);
  };

  const handleLogout = async () => {
    await signOutAction();
  };

  const renderContent = () => {
    switch (activeTab) {
      case "history":
        return <SubmissionHistory />;
      case "submit":
        return <SubmissionForm />;
      case "profile":
        return <UserProfile />;
      default:
        return <SubmissionHistory />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Top navigation bar */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="flex justify-between items-center py-2 px-6">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="gap-2 text-gray-600 hover:text-gray-900"
              >
                <Home className="h-4 w-4" />
                返回首页
              </Button>
            </Link>
            <div className="h-5 w-px bg-gray-300" />
            <Logo />
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              欢迎，{user.user_metadata?.name || user.email}
            </span>
            <Button
              variant="ghost"
              className="gap-2 text-gray-600 hover:text-gray-900"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              退出登录
            </Button>
          </div>
        </div>
      </header>

      {/* Main content area */}
      <div className="flex flex-1">
        {/* Left Sidebar */}
        <aside className="w-64 bg-white border-r border-gray-200 shadow-sm">
          <nav className="p-4">
            <div className="space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant="ghost"
                    className={cn(
                      "w-full justify-start gap-3 h-10 text-gray-700 hover:bg-gray-100 hover:text-gray-900 border border-transparent",
                      activeTab === item.id &&
                        "bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-600"
                    )}
                    onClick={() => handleTabChange(item.id)}
                  >
                    <Icon className="h-4 w-4" />
                    {item.label}
                  </Button>
                );
              })}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 flex flex-col bg-white">
          <div className="px-6 py-5 bg-white">
            <h1 className="text-xl font-semibold text-gray-900">
              {menuItems.find((item) => item.id === activeTab)?.label ||
                "个人中心"}
            </h1>
          </div>

          <div className="flex-1 overflow-auto bg-gray-50">
            <div className="p-6">{renderContent()}</div>
          </div>
        </main>
      </div>
    </div>
  );
}
