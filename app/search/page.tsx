"use client";

import { useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { CategorySidebar } from "@/components/category-sidebar";
import { ToolCard } from "@/components/tool-card";
import { SearchBar } from "@/components/search-bar";
import { AuthButton } from "@/components/auth-button";
import { Logo } from "@/components/logo";
import { mockToolsData } from "@/lib/mock-data";

export default function SearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchResults, setSearchResults] = useState<typeof mockToolsData>([]);

  useEffect(() => {
    if (query) {
      // Simple search implementation - filter tools by name and description
      const results = mockToolsData.filter(
        (tool) =>
          tool.name.toLowerCase().includes(query.toLowerCase()) ||
          tool.description.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [query]);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex justify-between items-center py-2 px-6">
          <div className="flex-shrink-0">
            <Logo />
          </div>
          <div className="flex-1 max-w-xl mx-6">
            <SearchBar />
          </div>
          <div className="flex-shrink-0">
            <AuthButton />
          </div>
        </div>
      </header>

      {/* Main content area with sidebar and content */}
      <div className="flex flex-1">
        {/* Left sidebar */}
        <CategorySidebar />

        {/* Right content area */}
        <main className="flex-1 overflow-auto">
          <div className="p-6 md:p-10">
            <div className="max-w-7xl mx-auto">
              {query ? (
                <div className="space-y-6">
                  <div>
                    <h1 className="text-2xl font-semibold mb-2">搜索结果</h1>
                    <p className="text-muted-foreground">
                      关键词 "{query}" 共找到 {searchResults.length} 个结果
                    </p>
                  </div>

                  {searchResults.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {searchResults.map((tool) => (
                        <ToolCard
                          key={tool.id}
                          id={tool.id}
                          name={tool.name}
                          description={tool.description}
                          logo={tool.logo}
                          category={tool.category}
                          slug={tool.slug}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="text-muted-foreground mb-4">
                        没有找到相关的AI工具
                      </div>
                      <p className="text-sm text-muted-foreground">
                        尝试使用不同的关键词或浏览我们的工具分类
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-muted-foreground mb-4">
                    请输入搜索关键词
                  </div>
                  <p className="text-sm text-muted-foreground">
                    您可以搜索工具名称或功能描述
                  </p>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
