"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

export default function TestSubmissionPage() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTestSubmit = async () => {
    setIsSubmitting(true);

    try {
      console.log("开始测试提交...");

      // 测试 Toast 功能
      toast({
        title: "测试 Toast",
        description: "这是一个测试通知",
      });

      console.log("Toast 测试完成");

      // 测试 API 调用（需要登录）
      const response = await fetch("/api/tool_submissions");
      console.log("API 响应状态:", response.status);

      if (response.status === 401) {
        toast({
          title: "需要登录",
          description: "请先登录后再测试",
          variant: "destructive",
        });
      } else {
        const data = await response.json();
        console.log("API 响应数据:", data);

        toast({
          title: "API 测试成功",
          description: `获取到 ${data.submissions?.length || 0} 条记录`,
        });
      }
    } catch (error) {
      console.error("测试错误:", error);
      toast({
        title: "测试失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">提交功能测试页面</h1>

      <div className="space-y-4">
        <Button
          onClick={handleTestSubmit}
          disabled={isSubmitting}
          className="w-full"
        >
          {isSubmitting ? "测试中..." : "测试 Toast 和 API"}
        </Button>

        <div className="text-sm text-gray-600">
          <p>这个页面用于测试：</p>
          <ul className="list-disc list-inside mt-2">
            <li>Toast 通知是否正常显示</li>
            <li>API 调用是否正常工作</li>
            <li>错误处理是否正确</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
