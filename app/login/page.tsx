"use client";

import type React from "react";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Logo } from "@/components/logo";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { signInAction, signUpAction } from "@/app/actions";
import { FormMessage } from "@/components/form-message";
import { Suspense } from "react";

export default function LoginPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex items-center py-2 px-6">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                返回首页
              </Button>
            </Link>
            <div className="h-6 w-px bg-border" />
            <Logo />
          </div>
        </div>
      </header>

      {/* Login content */}
      <div className="flex-1 flex items-center justify-center bg-secondary/30 p-4">
        <div className="w-full max-w-md">
          <Suspense fallback={<div>加载中...</div>}>
            <FormMessage />
          </Suspense>

          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">登录</TabsTrigger>
              <TabsTrigger value="register">注册</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <Card>
                <CardHeader>
                  <CardTitle>账号登录</CardTitle>
                  <CardDescription>
                    登录您的账号以访问个人中心和提交工具
                  </CardDescription>
                </CardHeader>
                <form action={signInAction}>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="请输入您的邮箱地址"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="password">密码</Label>
                        <Link
                          href="/forgot-password"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          忘记密码？
                        </Link>
                      </div>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        placeholder="请输入您的密码"
                        required
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full">
                      登录
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            <TabsContent value="register">
              <Card>
                <CardHeader>
                  <CardTitle>创建账号</CardTitle>
                  <CardDescription>
                    注册一个新账号以使用所有功能
                  </CardDescription>
                </CardHeader>
                <form action={signUpAction}>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="register-name">用户名</Label>
                      <Input
                        id="register-name"
                        name="name"
                        type="text"
                        placeholder="请输入您的用户名"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="register-email">邮箱</Label>
                      <Input
                        id="register-email"
                        name="email"
                        type="email"
                        placeholder="请输入您的邮箱地址"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="register-password">密码</Label>
                      <Input
                        id="register-password"
                        name="password"
                        type="password"
                        placeholder="请输入密码，至少6位字符"
                        required
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full">
                      注册
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
