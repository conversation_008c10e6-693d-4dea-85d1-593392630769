"use client";

import { CategorySidebar } from "@/components/category-sidebar";
import { ToolsGrid } from "@/components/tools-grid";
import { SearchBar } from "@/components/search-bar";
import { AuthButton } from "@/components/auth-button";
import { SubmitSiteButton } from "@/components/submit-site-button";
import { Logo } from "@/components/logo";
import { useState, useEffect } from "react";
import type { Category } from "@/types/category";

export default function Home() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        const data = await response.json();
        if (data.categories) {
          setCategories(data.categories);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex justify-between items-center py-2 px-6">
          <div className="flex-shrink-0">
            <Logo />
          </div>
          <div className="flex-1 max-w-xl mx-6">
            <SearchBar />
          </div>
          <div className="flex items-center gap-3 flex-shrink-0">
            <AuthButton />
            <SubmitSiteButton />
          </div>
        </div>
      </header>

      {/* Main content area with sidebar and content */}
      <div className="flex flex-1">
        {/* Left sidebar - now fixed */}
        <CategorySidebar />

        {/* Right content area - with left margin for sidebar */}
        <main className="flex-1 md:ml-64">
          <div className="p-6 md:p-10">
            <div className="max-w-7xl mx-auto">
              {/* Tools sections */}
              <div className="space-y-12">
                <section id="popular" className="scroll-mt-16">
                  <h2 className="text-2xl font-semibold mb-6">热门工具</h2>
                  <ToolsGrid category="popular" limit={6} />
                </section>

                <section id="new" className="scroll-mt-16">
                  <h2 className="text-2xl font-semibold mb-6">最新收录</h2>
                  <ToolsGrid category="new" limit={6} />
                </section>

                {/* Dynamic Categories sections */}
                {!loading &&
                  categories.map((category) => (
                    <section
                      key={category.id}
                      id={category.slug}
                      className="scroll-mt-16"
                    >
                      <h2 className="text-2xl font-semibold mb-6">
                        {category.name}
                      </h2>
                      <ToolsGrid category={category.slug} />
                    </section>
                  ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
