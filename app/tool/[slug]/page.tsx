import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { notFound } from "next/navigation";
import { ArrowUpRight, ArrowLeft } from "lucide-react";
import { Logo } from "@/components/logo";
import { SearchBar } from "@/components/search-bar";
import { AuthButton } from "@/components/auth-button";
import { Markdown } from "@/components/markdown";
import { createClient } from "@/lib/supabase/server";
import type { Tool } from "@/types/tool";

interface ToolPageProps {
  params: {
    slug: string;
  };
}

export default async function ToolPage({ params }: ToolPageProps) {
  const supabase = await createClient();

  // 从数据库获取工具详情
  const { data: tool, error } = await supabase
    .from("tools")
    .select(
      `
      *,
      category:categories(id, name, slug)
    `
    )
    .eq("slug", params.slug)
    .eq("status", "published")
    .single();

  if (error || !tool) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex justify-between items-center py-2 px-6">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                返回首页
              </Button>
            </Link>
            <div className="h-6 w-px bg-border" />
            <Logo />
          </div>
          <div className="flex-1 max-w-xl mx-6">
            <SearchBar />
          </div>
          <div className="flex-shrink-0">
            <AuthButton />
          </div>
        </div>
      </header>

      {/* Tool content */}
      <div className="flex-1">
        {/* Hero section with cover image */}
        <div className="relative h-64 md:h-80 w-full bg-gradient-to-r from-primary/20 to-primary/5">
          <div className="absolute inset-0 flex items-center justify-center">
            <Image
              src={
                tool.cover_url ||
                `/placeholder.svg?height=300&width=1200&query=${tool.name} cover image`
              }
              alt={`${tool.name} cover`}
              width={1200}
              height={300}
              className="object-cover w-full h-full"
              priority
            />
          </div>
        </div>

        {/* Tool info */}
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6 mb-8">
            <div className="w-20 h-20 rounded-xl overflow-hidden bg-secondary flex items-center justify-center">
              <Image
                src={tool.logo_url || "/placeholder.svg"}
                alt={tool.name}
                width={80}
                height={80}
                className="object-cover"
              />
            </div>

            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{tool.name}</h1>
              <div className="flex items-center gap-2 mb-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                  {tool.category?.name || "未分类"}
                </span>
                {tool.is_popular && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    热门
                  </span>
                )}
                {tool.is_new && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    最新
                  </span>
                )}
              </div>
              {tool.summary && (
                <p className="text-muted-foreground">{tool.summary}</p>
              )}
            </div>

            <Button className="gap-2" size="lg" asChild>
              <Link href={tool.url} target="_blank" rel="noopener noreferrer">
                打开网站
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>

          {/* Content */}
          <div className="space-y-8">
            {tool.description && (
              <section>
                <h2 className="text-xl font-semibold mb-4">工具简介</h2>
                <Markdown content={tool.description} />
              </section>
            )}

            {tool.faqs && (
              <section>
                <h2 className="text-xl font-semibold mb-4">常见问题</h2>
                <Markdown content={tool.faqs} />
              </section>
            )}

            {/* 如果没有详细描述和FAQ，显示默认内容 */}
            {!tool.description && !tool.faqs && (
              <section>
                <h2 className="text-xl font-semibold mb-4">工具简介</h2>
                <div className="prose max-w-none">
                  <p className="text-muted-foreground">
                    {tool.summary ||
                      "暂无详细介绍，请访问官方网站了解更多信息。"}
                  </p>
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
