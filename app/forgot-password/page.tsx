import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Logo } from "@/components/logo";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { forgotPasswordAction } from "@/app/actions";
import { FormMessage } from "@/components/form-message";
import { Suspense } from "react";

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation bar - full width */}
      <header className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex items-center py-2 px-6">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                返回首页
              </Button>
            </Link>
            <div className="h-6 w-px bg-border" />
            <Logo />
          </div>
        </div>
      </header>

      {/* Forgot password content */}
      <div className="flex-1 flex items-center justify-center bg-secondary/30 p-4">
        <div className="w-full max-w-md">
          <Suspense fallback={<div>加载中...</div>}>
            <FormMessage />
          </Suspense>

          <Card>
            <CardHeader>
              <CardTitle>忘记密码？</CardTitle>
              <CardDescription>
                输入您的邮箱地址，我们将发送重置链接
              </CardDescription>
            </CardHeader>
            <form action={forgotPasswordAction}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="forgot-email">邮箱</Label>
                  <Input
                    id="forgot-email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2">
                <Button type="submit" className="w-full">
                  发送重置链接
                </Button>
                <Link href="/login" className="w-full">
                  <Button variant="outline" className="w-full">
                    返回登录
                  </Button>
                </Link>
              </CardFooter>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
}
