export interface Tool {
  id: string;
  name: string;
  slug: string;
  url: string;
  logo_url: string | null;
  cover_url: string | null;
  category_id: string;
  summary: string | null;
  description: string | null;
  faqs: string | null;
  status: string;
  is_popular: boolean;
  is_new: boolean;
  submitted_by: string | null;
  created_at: string;
  updated_at: string;
  // 关联的分类信息（通过 JOIN 查询获得）
  category?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface ToolsApiResponse {
  tools: Tool[];
  total?: number;
}

export interface ToolApiResponse {
  tool: Tool | null;
}
