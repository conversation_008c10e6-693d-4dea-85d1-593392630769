export const mockToolsData = [
  {
    id: "1",
    name: "豆包",
    description: "字节跳动推出的免费AI智能助手",
    logo: "/placeholder.svg?height=48&width=48&query=DB",
    category: "ai-chat",
    slug: "doubao",
    isPopular: true,
    isNew: false,
  },
  {
    id: "2",
    name: "吐司文",
    description: "一键生成PPT和Word",
    logo: "/placeholder.svg?height=48&width=48&query=TW",
    category: "ai-office",
    slug: "toastword",
    isPopular: true,
    isNew: true,
  },
  {
    id: "3",
    name: "秘塔AI搜索",
    description: "最好用的AI搜索工具，没有广告",
    logo: "/placeholder.svg?height=48&width=48&query=MT",
    category: "ai-search",
    slug: "mita-search",
    isPopular: true,
    isNew: false,
  },
  {
    id: "4",
    name: "讯飞语文",
    description: "免费AI写作工具，5分钟生成高质量文章",
    logo: "/placeholder.svg?height=48&width=48&query=XF",
    category: "ai-writing",
    slug: "xunfei-writing",
    isPopular: true,
    isNew: false,
  },
  {
    id: "5",
    name: "美图设计室",
    description: "AI图像创作和设计平台",
    logo: "/placeholder.svg?height=48&width=48&query=MT",
    category: "ai-design",
    slug: "meitu-design",
    isPopular: false,
    isNew: true,
  },
  {
    id: "6",
    name: "稿定AI设计",
    description: "一站式AI设计与灵感创作平台",
    logo: "/placeholder.svg?height=48&width=48&query=GD",
    category: "ai-design",
    slug: "gaoding-design",
    isPopular: false,
    isNew: true,
  },
  {
    id: "7",
    name: "推文AI",
    description: "推荐优质的免费AI图像和出图工具",
    logo: "/placeholder.svg?height=48&width=48&query=TW",
    category: "ai-image",
    slug: "tuiwen-ai",
    isPopular: false,
    isNew: true,
  },
  {
    id: "8",
    name: "绘塔",
    description: "AI图像智能工具，免费生成高质量图片",
    logo: "/placeholder.svg?height=48&width=48&query=HT",
    category: "ai-image",
    slug: "huita",
    isPopular: true,
    isNew: false,
  },
  {
    id: "9",
    name: "Coze",
    description: "免费AI智能体免费用，已接入多个大模型",
    logo: "/placeholder.svg?height=48&width=48&query=CZ",
    category: "ai-chat",
    slug: "coze",
    isPopular: false,
    isNew: true,
  },
  {
    id: "10",
    name: "Suna",
    description: "全球首款通用型 AI Agent 开发平台",
    logo: "/placeholder.svg?height=48&width=48&query=SN",
    category: "ai-dev",
    slug: "suna",
    isPopular: false,
    isNew: true,
  },
  {
    id: "11",
    name: "拍子空间",
    description: "字节跳动推出的通用型 AI Agent 平台",
    logo: "/placeholder.svg?height=48&width=48&query=PZ",
    category: "ai-dev",
    slug: "paizi-space",
    isPopular: false,
    isNew: true,
  },
  {
    id: "12",
    name: "Z.ai",
    description: "智谱面向全球推出的通用型大语言模型",
    logo: "/placeholder.svg?height=48&width=48&query=ZA",
    category: "ai-chat",
    slug: "z-ai",
    isPopular: false,
    isNew: true,
  },
  // Add more mock data for each category
  {
    id: "13",
    name: "火山写作",
    description: "字节推出的免费AI写作助手",
    logo: "/placeholder.svg?height=48&width=48&query=HS",
    category: "ai-writing",
    slug: "huoshan-writing",
    isPopular: false,
    isNew: false,
  },
  {
    id: "14",
    name: "新华妙笔",
    description: "新华社推出的公众文章写作平台",
    logo: "/placeholder.svg?height=48&width=48&query=XH",
    category: "ai-writing",
    slug: "xinhua-writing",
    isPopular: false,
    isNew: false,
  },
  {
    id: "15",
    name: "魔搭AI",
    description: "免费提供AI模型开发平台，专为开发者设计",
    logo: "/placeholder.svg?height=48&width=48&query=MD",
    category: "ai-dev",
    slug: "modelscope",
    isPopular: false,
    isNew: false,
  },
]

export const categories = [
  { id: "ai-writing", name: "AI写作工具" },
  { id: "ai-image", name: "AI图像工具" },
  { id: "ai-video", name: "AI视频工具" },
  { id: "ai-office", name: "AI办公工具" },
  { id: "ai-design", name: "AI设计工具" },
  { id: "ai-chat", name: "AI对话聊天" },
  { id: "ai-code", name: "AI编程工具" },
  { id: "ai-search", name: "AI搜索引擎" },
  { id: "ai-audio", name: "AI音频工具" },
  { id: "ai-dev", name: "AI开发平台" },
  { id: "ai-translate", name: "AI语言翻译" },
  { id: "ai-research", name: "AI内容检测" },
  { id: "ai-legal", name: "AI法律助手" },
  { id: "ai-education", name: "AI学习网站" },
]
