import {
  PenLine,
  ImageIcon,
  Video,
  Briefcase,
  Paintbrush,
  MessageSquare,
  Code,
  Search,
  Music,
  Layers,
  BookIcon as BookTranslation,
  Brain,
  Scale,
  GraduationCap,
} from "lucide-react";

// 图标名称到组件的映射
const iconMap = {
  PencilIcon: PenLine,
  PhotoIcon: ImageIcon,
  VideoCameraIcon: Video,
  DocumentTextIcon: Briefcase,
  PaintBrushIcon: Paintbrush,
  ChatBubbleLeftRightIcon: MessageSquare,
  CodeBracketIcon: Code,
  MagnifyingGlassIcon: Search,
  SpeakerWaveIcon: Music,
  CpuChipIcon: Layers,
  LanguageIcon: BookTranslation,
  DocumentMagnifyingGlassIcon: Brain,
  ScaleIcon: Scale,
  AcademicCapIcon: GraduationCap,
};

export function getIconComponent(iconName: string | null) {
  if (!iconName || !iconMap[iconName as keyof typeof iconMap]) {
    return PenLine; // 默认图标
  }

  const IconComponent = iconMap[iconName as keyof typeof iconMap];
  return IconComponent;
}
